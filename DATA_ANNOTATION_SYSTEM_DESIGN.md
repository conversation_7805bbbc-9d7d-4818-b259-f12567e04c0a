# 数据标注系统设计方案

## 概述

本文档提供了基于现有企业管理系统框架改造成数据标注系统的详细设计方案。数据标注系统是一个专门用于管理和执行数据标注任务的平台，支持多种数据类型的标注工作，包括图像、文本、音频和视频等。

## 系统架构

保持原有的前后端分离架构：
- **前端**：React + TypeScript + Ant Design
- **后端**：FastAPI + SQLAlchemy + PostgreSQL

## UI 导航结构设计

### 顶部主导航模块

顶部导航栏将包含以下五个主要模块：

1. **首页** - 系统总览和个人工作台
2. **项目管理** - 标注项目的创建和管理
3. **数据集** - 数据集的导入、管理和导出
4. **标注工作台** - 进行实际标注工作的核心区域
5. **系统管理** - 系统设置、用户权限等管理功能

### 左侧导航菜单设计

#### 1. 首页模块
- **数据概览**
  - 标注进度统计
  - 质量评估指标
  - 个人工作量统计
- **我的任务**
  - 待处理任务
  - 已完成任务
  - 质检任务
- **消息通知**
  - 系统通知
  - 任务分配通知
  - 质检反馈

#### 2. 项目管理模块
- **项目列表**
  - 全部项目
  - 我参与的项目
  - 已归档项目
- **项目创建**
  - 基本信息设置
  - 标注规则配置
  - 标注团队组建
- **项目监控**
  - 进度监控
  - 质量监控
  - 成员表现
- **知识库**
  - 标注指南
  - 常见问题
  - 标准示例

#### 3. 数据集模块
- **数据集管理**
  - 数据集列表
  - 数据集详情
  - 数据集版本
- **数据导入**
  - 本地文件导入
  - 云存储导入
  - API导入
- **数据预处理**
  - 数据清洗
  - 数据增强
  - 数据分割
- **数据导出**
  - 导出设置
  - 导出记录
  - 格式转换

#### 4. 标注工作台模块
- **标注任务**
  - 待标注任务
  - 标注历史
  - 标注统计
- **标注工具**
  - 图像标注
  - 文本标注
  - 音频标注
  - 视频标注
- **质检任务**
  - 待质检任务
  - 质检历史
  - 质检统计
- **协作空间**
  - 疑难样本讨论
  - 标注规则讨论
  - 团队协作

#### 5. 系统管理模块
- **用户管理**
  - 用户列表
  - 角色权限
  - 团队管理
- **标注配置**
  - 标签体系管理
  - 标注模板管理
  - 快捷键设置
- **质量控制**
  - 质检规则
  - 一致性检查
  - 自动质检配置
- **系统设置**
  - 基础设置
  - 通知设置
  - 日志管理

## 角色与权限设计

为了支持数据标注系统的不同用户需求，系统将包含以下角色：

1. **管理员** - 拥有系统全部权限
2. **项目经理** - 负责项目创建、管理和监控
3. **标注员** - 执行标注任务
4. **质检员** - 负责质量检查和反馈
5. **数据工程师** - 负责数据集管理和预处理

## 实现思路

### 前端修改

1. **修改顶部导航菜单**

   在 `src/layouts/AppLayout.tsx` 中，修改 `topMenuItems` 数组：

   ```typescript
   const topMenuItems: MenuItem[] = [
     getItem('首页', '/home', <HomeOutlined />),
     getItem('项目管理', '/projects', <ProjectOutlined />),
     getItem('数据集', '/datasets', <DatabaseOutlined />),
     getItem('标注工作台', '/workspace', <AppstoreOutlined />),
     getItem('系统管理', '/system', <SettingOutlined />),
   ];
   ```

2. **创建各模块的侧边栏菜单**

   在 `src/layouts/AppLayout.tsx` 中，为每个顶部菜单创建对应的侧边栏菜单数组：

   ```typescript
   // 首页模块的侧边栏菜单
   const homeMenuItems: MenuItem[] = [
     getItem('数据概览', 'home-group', <DashboardOutlined />, [
       getItem('标注进度统计', 'progress-stats', <AreaChartOutlined />),
       getItem('质量评估指标', 'quality-metrics', <PieChartOutlined />),
       getItem('个人工作量统计', 'personal-stats', <BarChartOutlined />),
     ]),
     getItem('我的任务', 'tasks-group', <ProfileOutlined />, [
       getItem('待处理任务', 'pending-tasks', <ClockCircleOutlined />),
       getItem('已完成任务', 'completed-tasks', <CheckCircleOutlined />),
       getItem('质检任务', 'qa-tasks', <SafetyCertificateOutlined />),
     ]),
     getItem('消息通知', 'notification-group', <BellOutlined />, [
       getItem('系统通知', 'system-notifications', <NotificationOutlined />),
       getItem('任务分配通知', 'task-notifications', <SolutionOutlined />),
       getItem('质检反馈', 'qa-feedback', <CommentOutlined />),
     ]),
   ];

   // 项目管理模块的侧边栏菜单
   const projectMenuItems: MenuItem[] = [
     getItem('项目列表', 'project-list-group', <UnorderedListOutlined />, [
       getItem('全部项目', 'all-projects', <AppstoreOutlined />),
       getItem('我参与的项目', 'my-projects', <UserOutlined />),
       getItem('已归档项目', 'archived-projects', <InboxOutlined />),
     ]),
     getItem('项目创建', 'project-create-group', <PlusOutlined />, [
       getItem('基本信息设置', 'project-basic-info', <FormOutlined />),
       getItem('标注规则配置', 'annotation-rules', <ToolOutlined />),
       getItem('标注团队组建', 'annotation-team', <TeamOutlined />),
     ]),
     getItem('项目监控', 'project-monitor-group', <FundViewOutlined />, [
       getItem('进度监控', 'progress-monitor', <LineChartOutlined />),
       getItem('质量监控', 'quality-monitor', <PieChartOutlined />),
       getItem('成员表现', 'member-performance', <UserOutlined />),
     ]),
     getItem('知识库', 'knowledge-base-group', <ReadOutlined />, [
       getItem('标注指南', 'annotation-guides', <BookOutlined />),
       getItem('常见问题', 'faqs', <QuestionCircleOutlined />),
       getItem('标准示例', 'standard-examples', <FileImageOutlined />),
     ]),
   ];

   // 数据集模块的侧边栏菜单
   const datasetMenuItems: MenuItem[] = [
     getItem('数据集管理', 'dataset-manage-group', <DatabaseOutlined />, [
       getItem('数据集列表', 'dataset-list', <UnorderedListOutlined />),
       getItem('数据集详情', 'dataset-details', <FileSearchOutlined />),
       getItem('数据集版本', 'dataset-versions', <BranchesOutlined />),
     ]),
     getItem('数据导入', 'data-import-group', <ImportOutlined />, [
       getItem('本地文件导入', 'local-import', <UploadOutlined />),
       getItem('云存储导入', 'cloud-import', <CloudUploadOutlined />),
       getItem('API导入', 'api-import', <ApiOutlined />),
     ]),
     getItem('数据预处理', 'data-preprocess-group', <ExperimentOutlined />, [
       getItem('数据清洗', 'data-cleaning', <ClearOutlined />),
       getItem('数据增强', 'data-augmentation', <NodeExpandOutlined />),
       getItem('数据分割', 'data-splitting', <ScissorOutlined />),
     ]),
     getItem('数据导出', 'data-export-group', <ExportOutlined />, [
       getItem('导出设置', 'export-settings', <SettingOutlined />),
       getItem('导出记录', 'export-records', <HistoryOutlined />),
       getItem('格式转换', 'format-conversion', <SwapOutlined />),
     ]),
   ];

   // 标注工作台模块的侧边栏菜单
   const workspaceMenuItems: MenuItem[] = [
     getItem('标注任务', 'annotation-tasks-group', <CheckSquareOutlined />, [
       getItem('待标注任务', 'pending-annotations', <ClockCircleOutlined />),
       getItem('标注历史', 'annotation-history', <HistoryOutlined />),
       getItem('标注统计', 'annotation-stats', <BarChartOutlined />),
     ]),
     getItem('标注工具', 'annotation-tools-group', <ToolOutlined />, [
       getItem('图像标注', 'image-annotation', <FileImageOutlined />),
       getItem('文本标注', 'text-annotation', <FileTextOutlined />),
       getItem('音频标注', 'audio-annotation', <AudioOutlined />),
       getItem('视频标注', 'video-annotation', <VideoCameraOutlined />),
     ]),
     getItem('质检任务', 'qa-tasks-group', <SafetyCertificateOutlined />, [
       getItem('待质检任务', 'pending-qa', <ClockCircleOutlined />),
       getItem('质检历史', 'qa-history', <HistoryOutlined />),
       getItem('质检统计', 'qa-stats', <BarChartOutlined />),
     ]),
     getItem('协作空间', 'collaboration-group', <TeamOutlined />, [
       getItem('疑难样本讨论', 'difficult-samples', <QuestionCircleOutlined />),
       getItem('标注规则讨论', 'rule-discussions', <CommentOutlined />),
       getItem('团队协作', 'team-collaboration', <UserSwitchOutlined />),
     ]),
   ];

   // 系统管理模块的侧边栏菜单
   const systemMenuItems: MenuItem[] = [
     getItem('用户管理', 'user-manage-group', <UserOutlined />, [
       getItem('用户列表', 'user-list', <TeamOutlined />),
       getItem('角色权限', 'roles-permissions', <SafetyCertificateOutlined />),
       getItem('团队管理', 'team-management', <ClusterOutlined />),
     ]),
     getItem('标注配置', 'annotation-config-group', <SettingOutlined />, [
       getItem('标签体系管理', 'label-management', <TagsOutlined />),
       getItem('标注模板管理', 'template-management', <FormOutlined />),
       getItem('快捷键设置', 'shortcut-settings', <ControlOutlined />),
     ]),
     getItem('质量控制', 'quality-control-group', <SafetyOutlined />, [
       getItem('质检规则', 'qa-rules', <AuditOutlined />),
       getItem('一致性检查', 'consistency-check', <CheckCircleOutlined />),
       getItem('自动质检配置', 'auto-qa-config', <RobotOutlined />),
     ]),
     getItem('系统设置', 'system-settings-group', <ToolOutlined />, [
       getItem('基础设置', 'basic-settings', <SettingOutlined />),
       getItem('通知设置', 'notification-settings', <BellOutlined />),
       getItem('日志管理', 'log-management', <FileTextOutlined />),
     ]),
   ];
   ```

3. **修改 `getMenuItemsByTopMenu` 函数**

   ```typescript
   const getMenuItemsByTopMenu = (topMenuItem: string): MenuItem[] => {
     switch (topMenuItem) {
       case '/home':
         return homeMenuItems;
       case '/projects':
         return projectMenuItems;
       case '/datasets':
         return datasetMenuItems;
       case '/workspace':
         return workspaceMenuItems;
       case '/system':
         return systemMenuItems;
       default:
         return homeMenuItems;
     }
   };
   ```

4. **更新面包屑映射**

   在 `AppLayout.tsx` 中，更新 `breadcrumbMap` 对象，为新的导航结构添加面包屑路径。

5. **添加路由配置**

   在 `src/router/index.tsx` 中，更新路由配置以匹配新的导航结构。

### 后端修改

1. **数据模型设计**

   在 `app/models/` 目录下，创建以下新的数据模型：
   - `project.py` - 项目模型
   - `dataset.py` - 数据集模型
   - `annotation.py` - 标注模型
   - `task.py` - 任务模型
   - `label.py` - 标签模型

2. **API 端点设计**

   在 `app/api/endpoints/` 目录下，创建对应的 API 端点文件：
   - `projects.py`
   - `datasets.py`
   - `annotations.py`
   - `tasks.py`
   - `labels.py`

3. **数据库关系**

   设计主要实体之间的关系：
   - 项目包含多个数据集
   - 数据集包含多个数据项
   - 数据项可以有多个标注
   - 用户可以参与多个项目
   - 任务分配给特定用户

## 核心功能实现建议

### 1. 标注工具界面

标注工具界面是系统的核心，需要根据不同的数据类型（图像、文本、音频、视频）设计不同的标注界面。可以考虑使用或集成以下开源工具：

- 图像标注：React 版本的 [labelme](https://github.com/wkentaro/labelme) 或 [CVAT](https://github.com/opencv/cvat)
- 文本标注：[doccano](https://github.com/doccano/doccano) 的前端组件
- 音频标注：自定义的音频波形显示和标记工具
- 视频标注：基于 [VideoJS](https://videojs.com/) 的自定义标注工具

### 2. 数据集管理

数据集管理需要支持大文件上传、分块处理和版本控制。建议：

- 使用 S3 兼容的对象存储服务存储原始数据
- 实现断点续传和分片上传
- 设计版本控制机制，记录数据集的变更历史

### 3. 质量控制

质量控制是保证标注质量的关键，可以实现：

- 多人标注一致性检查
- 随机抽查机制
- 标注规则自动检查
- 标注速度和质量的统计分析

### 4. 任务分配与管理

任务分配系统需要考虑：

- 基于用户技能和历史表现的智能分配
- 任务优先级管理
- 任务进度跟踪
- 工作量均衡分配

## 用户体验设计建议

1. **标注效率**
   - 提供快捷键支持
   - 设计直观的标注界面
   - 支持批量操作

2. **协作功能**
   - 实时评论和讨论
   - 标注问题标记和解决流程
   - 知识库和标准示例参考

3. **数据可视化**
   - 标注进度和质量的实时仪表盘
   - 项目状态的可视化展示
   - 团队表现的对比分析

## 下一步实施计划

1. 首先实现核心的项目管理和数据集管理功能
2. 然后开发基本的图像标注工具
3. 接着实现任务分配和质量控制功能
4. 最后扩展到其他数据类型的标注支持

通过这种渐进式的开发方法，可以确保系统在每个阶段都是可用的，并且可以根据用户反馈不断改进。 