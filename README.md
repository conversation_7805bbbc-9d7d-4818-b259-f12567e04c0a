# 企业管理系统（Enterprise Management System）

## 1. 项目概述

本项目是一个前后端分离的企业级管理系统，旨在提供一个功能全面、模块化、可扩展的管理平台。它构建于现代化的技术栈之上，拥有清晰的架构和强大的功能基础，是进行二次开发的理想起点。

### 核心技术栈

**后端 (Backend):**
- **框架**: FastAPI
- **数据库**: SQLAlchemy (ORM) with AsyncPG (PostgreSQL 异步驱动)
- **认证**: JWT (JSON Web Tokens)
- **环境**: Python 3.10+

**前端 (Frontend):**
- **框架**: React 19 + TypeScript
- **UI 组件库**: Ant Design 5.x
- **构建工具**: Vite
- **路由**: React Router 7.x
- **状态管理**: Zustand
- **HTTP客户端**: Axios

## 2. 环境准备与启动

在开始之前，请确保您的开发环境中已安装以下软件：
- [Node.js](https://nodejs.org/) (v18.x 或更高版本)
- [Python](https://www.python.org/) (v3.10 或更高版本)
- [PostgreSQL](https://www.postgresql.org/) 数据库

---

### 后端启动步骤

1.  **进入后端目录**
    ```powershell
    cd backend
    ```

2.  **创建并激活 Python 虚拟环境**
    ```powershell
    python -m venv venv
    .\venv\Scripts\activate
    ```
    *在 Linux 或 macOS 上，激活命令为 `source venv/bin/activate`*

3.  **安装依赖**
    ```powershell
    pip install -r requirements.txt
    ```

4.  **数据库配置**
    -  在 PostgreSQL 中创建一个新的数据库。
    -  项目的数据库连接配置位于 `backend/app/core/config.py`。请根据您的本地环境修改 `SQLALCHEMY_DATABASE_URI`。
    -  **注意**: 本项目目前没有集成数据库迁移工具（如 Alembic），在修改 `models` 后需要手动同步数据库表结构。

5.  **启动后端服务**
    ```powershell
    uvicorn app.main:app --reload
    ```
    服务将在 `http://127.0.0.1:8000` 上运行。您可以在 `http://127.0.0.1:8000/docs` 查看自动生成的 API 文档。

---

### 前端启动步骤

1.  **进入前端目录**
    ```powershell
    cd frontend
    ```

2.  **安装依赖**
    ```powershell
    npm install
    ```
    *如果 `npm` 安装缓慢或失败，可以尝试使用 `pnpm` 或 `yarn`。*

3.  **配置代理**
    - 前端通过 Vite 代理将 `/api/v1` 请求转发到后端。此配置位于 `frontend/vite.config.ts` 文件中，默认指向 `http://127.0.0.1:8000`。如果您的后端服务地址不同，请在此处修改。

4.  **启动前端开发服务**
    ```powershell
    npm run dev
    ```
    服务通常会在 `http://localhost:5173` 上启动。


## 3. 二次开发指南与约束

为了确保项目的长期稳定性和可维护性，二次开发应严格遵循以下指南和约束。核心原则是 **"新增而非修改"**，尽量在不改动核心架构的基础上添加新功能。

---

### 后端开发指南

后端采用了模块化的设计，添加新功能应遵循现有模式。

**核心约束 (非必要不应修改):**
- `app/main.py`: 应用主入口和中间件配置。
- `app/core/`: 存放项目的核心配置和安全逻辑，尤其是 `security.py` 中的认证流程。
- `app/db/session.py`: 数据库会话管理。
- `app/api/deps.py`: API 依赖注入项。

**如何添加一个新的业务模块 (例如 "仓库管理"):**

1.  **定义数据模型**: 在 `app/models/` 目录下创建 `warehouse.py`，使用 SQLAlchemy 定义数据表结构。
2.  **定义数据结构 (Schema)**: 在 `app/schemas/` 目录下创建 `warehouse.py`，使用 Pydantic 定义 API 的请求和响应数据结构。
3.  **创建 CRUD 操作**: 在 `app/crud/` 目录下创建 `crud_warehouse.py`，封装对仓库模型的数据库操作函数。
4.  **创建 API 端点**: 在 `app/api/endpoints/` 目录下创建 `warehouse.py`，定义相关的 API 路由 (e.g., `POST /warehouses`, `GET /warehouses/{id}`)。
5.  **集成新路由**: 在 `app/api/api.py` 中，导入并包含您在 `warehouse.py` 中创建的路由器。

---

### 前端开发指南

前端以 `AppLayout` 为核心布局，并使用 `React Router` 进行页面路由。

**核心约束 (非必要不应修改):**
- `src/layouts/AppLayout.tsx`: 这是整个应用的**核心布局文件**。二次开发应通过修改其内部的**数据结构 (菜单数组)**来添加菜单项，而非直接修改其组件渲染逻辑。
- `src/router/ProtectedRoute.tsx`: 路由守卫，控制用户访问权限。
- `src/store/authStore.ts`: 全局认证状态管理，封装了登录和登出的核心逻辑。
- `src/api/client.ts`: 全局 Axios 实例配置，统一处理 API 请求和 Token 注入。

**如何添加一个新的功能页面 (例如 "仓库列表"):**

1.  **创建页面组件**: 在 `src/pages/` 目录下创建一个新的组件文件，例如 `WarehouseListPage.tsx`。
2.  **添加菜单项**:
    - 打开 `src/layouts/AppLayout.tsx`。
    - 找到对应的菜单数组（例如 `workbenchMenuItems`），在其中添加新的菜单项，定义其 `label`, `key`, 和 `icon`。`key` 的值将作为页面的 URL 路径。
    - **示例**:
      ```typescript
      // 在 "产品管理" 子菜单下添加 "仓库管理"
      getItem('产品管理', 'product-group', <AppstoreOutlined />, [
        // ... 其他菜单项
        getItem('仓库管理', 'warehouses', <DatabaseOutlined />) 
      ]),
      ```
3.  **更新面包屑**: 在 `AppLayout.tsx` 的 `breadcrumbMap` 对象中添加新页面的路径映射。
    ```typescript
    'warehouses': ['产品管理', '仓库管理'],
    ```
4.  **添加路由**:
    - 打开 `src/router/index.tsx`。
    - 在相应的模块（例如 `workbench` 的 `children`）下，找到之前为占位符 `EmptyPage` 设置的路由，或创建一个新路由。
    - 将 `element` 从 `createEmptyPage(...)()` 替换为您新建的页面组件。
    - **示例**:
      ```typescript
      // 替换之前的空页面
      { path: 'warehouses', element: <WarehouseListPage /> },
      ```
5.  **API 调用**: 在你的新页面组件中，从 `src/api/client.ts` 导入共享的 `client` 实例来发起后端请求。

通过遵循以上指南，我们可以确保所有二次开发都建立在稳定可靠的基础之上，最大程度地降低项目风险。 