# 项目需求文档：前后端分离脚手架

## 1. 项目概述

本项目旨在搭建一个功能完善、结构清晰的前后端分离的应用脚手架。前端将采用 React 和 Ant Design 构建现代化用户界面，后端则使用 Python、FastAPI 和 PostgreSQL 提供高效稳定的 API 服务。项目将实现一个基础的带有认证流程的用户登录功能，并构建一个经典的后台管理页面布局。

## 2. 技术栈

*   **前端**:
    *   **UI 框架**: React 18.x
    *   **组件库**: Ant Design 5.x
    *   **包管理器**: Vite
    *   **路由**: React Router DOM
    *   **状态管理**: Zustand (轻量级，易于上手)
    *   **HTTP 请求**: Axios

*   **后端**:
    *   **Web 框架**: FastAPI
    *   **Python 版本**: 3.9+
    *   **数据库**: PostgreSQL
    *   **ORM**: SQLAlchemy 2.x (异步模式)
    *   **密码处理**: passlib, bcrypt
    *   **认证**: JWT (JSON Web Tokens)
    *   **依赖管理**: Poetry

*   **数据库**:
    *   **类型**: PostgreSQL
    *   **数据库名**: `webbasedb2`
    *   **用户名**: `postgres`
    *   **密码**: `postgres`
    *   **地址**: `localhost:5432`

## 3. 功能需求

### 3.1 用户认证模块

*   **登录页面**:
    *   提供用户名和密码输入框。
    *   提供"登录"按钮。
    *   对输入内容进行基本的前端校验（如：不能为空）。

*   **登录接口**:
    *   后端提供 `/api/login` 接口，接收用户名和密码。
    *   验证成功后，生成一个 JWT Token 并返回给前端。
    *   验证失败，返回相应的错误信息。

*   **密码安全**:
    *   用户密码在存入数据库前，必须经过加盐哈希处理。

*   **路由保护**:
    *   除登录页外，所有其他前端页面都需要在用户登录后才能访问。
    *   未登录用户访问受保护页面时，应自动跳转到登录页。
    *   前端请求后端受保护接口时，需在请求头中携带 JWT Token。

### 3.2 首页布局

*   **整体布局**:
    *   成功登录后，用户将被重定向到应用首页 (`/`)。
    *   首页采用经典的后台管理布局。

*   **顶部导航栏**:
    *   位于页面顶部，用于展示一级功能模块。
    *   示例模块: "Dashboard", "系统管理", "用户中心"。

*   **左侧菜单栏**:
    *   位于页面左侧，根据顶部选中的一级模块，动态展示对应的二级子菜单。
    *   例如，当顶部选中"系统管理"时，左侧可显示"用户管理"、"角色管理"等子菜单。
    *   点击子菜单，右侧内容区域会切换到对应的功能页面。

*   **内容区域**:
    *   页面的主要工作区，用于展示具体的功能内容。

## 4. 数据库设计

*   **`users` 表**: 用于存储用户信息。
    *   `id` (Integer, Primary Key, Auto-increment)
    *   `username` (String, Unique, Not Null)
    *   `hashed_password` (String, Not Null)
    *   `created_at` (Timestamp, Default: CURRENT_TIMESTAMP)

## 5. 项目结构 (初步规划)

```
/project-root
├── /backend          # FastAPI 后端项目
│   ├── /app
│   │   ├── /api
│   │   ├── /core
│   │   ├── /db
│   │   ├── /models
│   │   └── /schemas
│   ├── pyproject.toml
│   └── poetry.lock
│
└── /frontend         # React 前端项目
    ├── /public
    ├── /src
    │   ├── /api
    │   ├── /assets
    │   ├── /components
    │   ├── /layouts
    │   ├── /pages
    │   ├── /router
    │   └── /store
    ├── package.json
    └── vite.config.js
```

## 6. 开发计划

1.  **环境搭建**: 初始化前后端项目结构，安装所需依赖。
2.  **后端开发**:
    *   配置数据库连接。
    *   创建 `users` 数据模型 (Model) 和数据校验模型 (Schema)。
    *   实现密码哈希与验证逻辑。
    *   开发登录接口，集成 JWT 认证。
    *   创建受保护的示例接口。
3.  **前端开发**:
    *   使用 Vite 初始化 React + Ant Design 项目。
    *   配置 React Router。
    *   创建登录页面组件。
    *   创建首页的整体布局组件（顶部+左侧+内容区）。
    *   封装 Axios 请求，实现 Token 的自动携带和响应处理。
4.  **前后端联调**:
    *   前端调用登录接口，成功后保存 Token 并跳转至首页。
    *   实现路由守卫，未登录则跳转回登录页。
    *   实现首页菜单的动态展示逻辑。
    *   完成整个登录 -> 首页展示的流程。

---

请您审阅以上需求文档。如果内容准确无误，我将按照此文档开始具体的开发工作。 