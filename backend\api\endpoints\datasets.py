from typing import Any
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from backend.schemas import Dataset, DatasetUpdate
from backend.crud import dataset as crud
from backend.models import User
from backend.api import deps

router = APIRouter()

@router.put("/{dataset_id}", response_model=Dataset)
async def update_dataset(
    *,
    db: AsyncSession = Depends(deps.get_db),
    dataset_id: int,
    dataset_in: DatasetUpdate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update a dataset.
    """
    dataset = await crud.dataset.get(db=db, id=dataset_id)
    if not dataset:
        raise HTTPException(status_code=404, detail="Dataset not found")
    # TODO: Improve permission check to verify project ownership
    if dataset.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    dataset = await crud.dataset.update(db=db, db_obj=dataset, obj_in=dataset_in)
    return dataset


@router.delete("/{dataset_id}", response_model=Dataset)
async def delete_dataset(
    *,
    db: AsyncSession = Depends(deps.get_db),
    dataset_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Delete a dataset.
    """
    dataset = await crud.dataset.get(db=db, id=dataset_id)
    if not dataset:
        raise HTTPException(status_code=404, detail="Dataset not found")
    # TODO: Improve permission check to verify project ownership
    if dataset.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    dataset = await crud.dataset.remove(db=db, id=dataset_id)
    return dataset


@router.post("/", response_model=Dataset)
async def create_dataset(
    *,
    db: AsyncSession = Depends(deps.get_db),
    dataset_in: DatasetCreate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create new dataset.
    """
    # TODO: Add authorization check to ensure user has access to the project
    dataset = await crud.dataset.create_with_owner_and_project(db=db, obj_in=dataset_in, owner_id=current_user.id)
    return dataset 