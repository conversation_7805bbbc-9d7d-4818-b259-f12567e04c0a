from typing import Any, List

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, models, schemas
from app.api import deps

router = APIRouter()


@router.get("/", response_model=List[schemas.Dataset])
async def read_datasets(
    *,
    db: AsyncSession = Depends(deps.get_db),
    project_id: int,
    skip: int = 0,
    limit: int = 100,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve datasets for a specific project.
    """
    # TODO: Add authorization check to ensure user has access to the project
    datasets = await crud.dataset.get_multi_by_project(db, project_id=project_id, skip=skip, limit=limit)
    return datasets


@router.post("/", response_model=schemas.Dataset)
async def create_dataset(
    *,
    db: AsyncSession = Depends(deps.get_db),
    dataset_in: schemas.DatasetCreate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create new dataset.
    """
    # TODO: Add authorization check to ensure user has access to the project
    dataset = await crud.dataset.create_with_owner_and_project(db=db, obj_in=dataset_in, owner_id=current_user.id)
    return dataset 