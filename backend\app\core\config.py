import secrets
from typing import Any, Dict, List, Optional, Union
from pydantic import AnyHttpUrl, PostgresDsn, field_validator, validator
from pydantic_settings import BaseSettings
from functools import lru_cache
import logging

logger = logging.getLogger(__name__)

class Settings(BaseSettings):
    # 应用设置
    PROJECT_NAME: str = "数据标注系统"
    API_V1_STR: str = "/api/v1"
    DEBUG: bool = True
    
    # 安全设置
    SECRET_KEY: str = secrets.token_urlsafe(32)
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 7  # 7 days
    
    # 数据库设置
    POSTGRES_SERVER: str = "127.0.0.1"
    POSTGRES_USER: str = "postgres"
    POSTGRES_PASSWORD: str = "postgres"
    POSTGRES_DB: str = "app"
    POSTGRES_PORT: str = "5432"
    
    # CORS 设置
    BACKEND_CORS_ORIGINS: List[str] = ["http://localhost:5173", "http://localhost:5174", "http://localhost:5175"]
    
    @field_validator("BACKEND_CORS_ORIGINS")
    @classmethod
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
    
    @property
    def DATABASE_URL(self) -> str:
        """构建数据库 URL"""
        return f"postgresql+asyncpg://{self.POSTGRES_USER}:{self.POSTGRES_PASSWORD}@{self.POSTGRES_SERVER}:{self.POSTGRES_PORT}/{self.POSTGRES_DB}"
    
    # 日志设置
    LOG_LEVEL: str = "INFO"
    
    # 其他设置
    INITIAL_ADMIN_EMAIL: str = "<EMAIL>"
    INITIAL_ADMIN_PASSWORD: str = "password"
    
    class Config:
        env_file = ".env"
        case_sensitive = True
        
        # 环境变量前缀
        env_prefix = "APP_"

@lru_cache()
def get_settings() -> Settings:
    """
    获取应用配置，使用 lru_cache 进行缓存
    """
    settings = Settings()
    logger.info("加载应用配置")
    return settings

# 导出设置实例
settings = get_settings() 