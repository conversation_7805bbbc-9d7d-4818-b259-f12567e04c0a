import logging
from typing import Callable, Dict, Any
from fastapi import <PERSON><PERSON><PERSON>, Request, Response, status
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.exceptions import HTTPException as StarletteHTTPException
import time
import traceback

# 配置日志
logger = logging.getLogger(__name__)

class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """
    请求日志中间件，记录所有请求的处理时间和结果
    """
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()
        
        # 记录请求信息
        logger.info(f"Request started: {request.method} {request.url.path}")
        
        try:
            response = await call_next(request)
            process_time = time.time() - start_time
            response.headers["X-Process-Time"] = str(process_time)
            
            # 记录响应信息
            logger.info(
                f"Request completed: {request.method} {request.url.path} "
                f"- Status: {response.status_code} - Duration: {process_time:.4f}s"
            )
            return response
        except Exception as e:
            process_time = time.time() - start_time
            logger.exception(
                f"Request failed: {request.method} {request.url.path} "
                f"- Error: {str(e)} - Duration: {process_time:.4f}s"
            )
            raise

def setup_exception_handlers(app: FastAPI) -> None:
    """
    设置全局异常处理器
    """
    @app.exception_handler(StarletteHTTPException)
    async def http_exception_handler(request: Request, exc: StarletteHTTPException) -> JSONResponse:
        """处理 HTTP 异常"""
        return JSONResponse(
            status_code=exc.status_code,
            content={"detail": exc.detail},
            headers=exc.headers,
        )
    
    @app.exception_handler(RequestValidationError)
    async def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
        """处理请求验证错误"""
        errors = []
        for error in exc.errors():
            error_info = {
                "loc": " -> ".join([str(loc) for loc in error["loc"]]),
                "msg": error["msg"],
                "type": error["type"],
            }
            errors.append(error_info)
        
        return JSONResponse(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            content={
                "detail": "Validation error",
                "errors": errors
            },
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
        """处理所有其他未捕获的异常"""
        logger.exception(f"Unhandled exception: {str(exc)}")
        
        # 在开发环境中返回详细错误信息，生产环境则返回通用错误消息
        error_detail = {
            "detail": "Internal server error",
            "message": str(exc),
            "traceback": traceback.format_exc().split("\n")
        }
        
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=error_detail,
        )

def setup_middlewares(app: FastAPI) -> None:
    """
    设置所有中间件
    """
    # 添加请求日志中间件
    app.add_middleware(RequestLoggingMiddleware)
    
    # 设置异常处理器
    setup_exception_handlers(app) 