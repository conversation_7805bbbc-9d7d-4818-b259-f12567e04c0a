from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from fastapi.encoders import jsonable_encoder

from app.crud.base import CRUDBase
from app.models.dataset import Dataset
from app.schemas.dataset import DatasetCreate, DatasetUpdate

class CRUDDataset(CRUDBase[Dataset, DatasetCreate, DatasetUpdate]):
    async def create_with_owner_and_project(
        self, db: AsyncSession, *, obj_in: DatasetCreate, owner_id: int
    ) -> Dataset:
        obj_in_data = jsonable_encoder(obj_in)
        db_obj = self.model(**obj_in_data, owner_id=owner_id)
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def get_multi_by_project(
        self, db: AsyncSession, *, project_id: int, skip: int = 0, limit: int = 100
    ) -> list[Dataset]:
        result = await db.execute(
            select(self.model)
            .filter(Dataset.project_id == project_id)
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

dataset = CRUDDataset(Dataset) 