from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from sqlalchemy.orm import selectinload
from fastapi.encoders import jsonable_encoder
import typing as t
from typing import List

from app.crud.base import CRUDBase
from app.models.project import Project
from app.schemas.project import ProjectCreate, ProjectUpdate

class CRUDProject(CRUDBase[Project, ProjectCreate, ProjectUpdate]):
    async def get(self, db: AsyncSession, id: t.Any) -> Project | None:
        query = select(self.model).where(self.model.id == id).options(selectinload(self.model.owner), selectinload(self.model.members))
        result = await db.execute(query)
        return result.scalars().first()

    async def get_multi(
        self, db: AsyncSession, *, skip: int = 0, limit: int = 100
    ) -> list[Project]:
        query = select(self.model).offset(skip).limit(limit).options(selectinload(self.model.owner), selectinload(self.model.members))
        result = await db.execute(query)
        return result.scalars().all()

    async def create_with_owner(
        self, db: AsyncSession, *, obj_in: ProjectCreate, owner_id: int
    ) -> Project:
        db_obj = self.model(**obj_in.dict(), owner_id=owner_id)
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def get_multi_by_owner(
        self, db: AsyncSession, *, owner_id: int, skip: int = 0, limit: int = 100
    ) -> List[Project]:
        result = await db.execute(
            select(self.model)
            .filter(Project.owner_id == owner_id)
            .offset(skip)
            .limit(limit)
            .order_by(self.model.id)
        )
        return result.scalars().all()
    
    async def get_project_by_name(self, db: AsyncSession, *, name: str) -> Project | None:
        result = await db.execute(select(self.model).filter(self.model.name == name))
        return result.scalars().first()

project = CRUDProject(Project) 