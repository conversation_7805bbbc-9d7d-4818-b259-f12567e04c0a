import logging
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager

from app.api.api import api_router
from app.core.config import settings
from app.core.logging import setup_logging
from app.core.middleware import setup_middlewares
from app.db.session import engine
from app.db.base import Base

# 设置日志
logger = setup_logging()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    应用生命周期管理
    """
    # 启动时执行
    logger.info("应用启动中...")
    
    # 创建数据库表
    async with engine.begin() as conn:
        # 在生产环境中，应使用 Alembic 进行数据库迁移
        # 这里仅为开发环境提供便利
        if settings.DEBUG:
            logger.info("创建数据库表...")
            await conn.run_sync(Base.metadata.create_all)
    
    # 应用启动完成
    logger.info(f"应用 '{settings.PROJECT_NAME}' 启动完成")
    
    yield
    
    # 关闭时执行
    logger.info("应用关闭中...")
    
    # 关闭数据库连接
    await engine.dispose()
    
    logger.info("应用已关闭")

# 创建应用
app = FastAPI(
    title=settings.PROJECT_NAME,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    description="数据标注系统 API",
    version="0.1.0",
    lifespan=lifespan,
    debug=settings.DEBUG
)

# 设置 CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=[str(origin).strip("/") for origin in settings.BACKEND_CORS_ORIGINS],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 设置中间件
setup_middlewares(app)

# 注册路由
app.include_router(api_router, prefix=settings.API_V1_STR)

# 添加健康检查端点
@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {"status": "ok"} 