from sqlalchemy import Column, Integer, String, ForeignKey
from sqlalchemy.orm import relationship

from app.db.base_class import Base
from app.models.project_user import project_user_association

class Project(Base):
    __tablename__ = "projects"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True, nullable=False)
    description = Column(String, nullable=True)
    owner_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    owner = relationship("User", back_populates="owned_projects")
    datasets = relationship("Dataset", back_populates="project", cascade="all, delete-orphan")
    # labels = relationship("Label", back_populates="project", cascade="all, delete-orphan")
    
    members = relationship(
        "User",
        secondary=project_user_association,
        back_populates="member_of_projects"
    ) 