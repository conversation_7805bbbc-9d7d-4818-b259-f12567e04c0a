from sqlalchemy import Column, Integer, String, DateTime, Boolean
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.db.base_class import Base
from app.models.project_user import project_user_association

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String, unique=True, index=True, nullable=False)
    hashed_password = Column(String, nullable=False)
    role = Column(String, default="annotator", nullable=False) # Roles: admin, manager, annotator, auditor
    is_active = Column(Boolean(), default=True)
    is_superuser = Column(Boolean(), default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    owned_projects = relationship(
        "Project", 
        back_populates="owner", 
        cascade="all, delete-orphan"
    )
    member_of_projects = relationship(
        "Project",
        secondary=project_user_association,
        back_populates="members"
    )
    datasets = relationship("Dataset", back_populates="owner", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<User(username={self.username}, email={self.email})>" 