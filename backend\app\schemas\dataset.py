from pydantic import BaseModel

# Shared properties
class DatasetBase(BaseModel):
    name: str
    description: str | None = None

# Properties to receive on dataset creation
class DatasetCreate(DatasetBase):
    pass

# Properties to receive on dataset update
class DatasetUpdate(DatasetBase):
    pass

# Properties shared by models stored in DB
class DatasetInDBBase(DatasetBase):
    id: int
    project_id: int

    class Config:
        from_attributes = True

# Properties to return to client
class Dataset(DatasetInDBBase):
    pass

# Properties stored in DB
class DatasetInDB(DatasetInDBBase):
    pass 