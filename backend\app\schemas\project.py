from __future__ import annotations
from typing import List, Optional
from pydantic import BaseModel, ConfigDict
# from app.schemas.user import User # This import causes the circular dependency
from .user import UserSimple

# Shared properties
class ProjectBase(BaseModel):
    name: str
    description: str | None = None

# A minimal project representation for nesting in other schemas
class ProjectSimple(BaseModel):
    id: int
    name: str
    description: str | None = None
    
    model_config = ConfigDict(from_attributes=True)

# Properties to receive on project creation
class ProjectCreate(ProjectBase):
    pass

# Properties to receive on project update
class ProjectUpdate(ProjectBase):
    pass

# Properties shared by models stored in DB
class ProjectInDBBase(ProjectBase):
    id: int
    owner_id: int

    model_config = ConfigDict(from_attributes=True)

# Properties to return to client
class Project(ProjectInDBBase):
    owner: UserSimple
    members: List[UserSimple] = []

# Properties stored in DB
class ProjectInDB(ProjectInDBBase):
    pass

# To resolve the forward reference, we need to import User here
# so that it's in scope when update_forward_refs is called.
from .user import User
Project.update_forward_refs() 