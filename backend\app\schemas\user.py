from __future__ import annotations
from typing import Optional, List
from pydantic import BaseModel, ConfigDict, EmailStr
from datetime import datetime

# A minimal user representation for nesting in other schemas
class UserSimple(BaseModel):
    id: int
    username: str
    email: EmailStr
    role: str
    is_active: bool
    model_config = ConfigDict(from_attributes=True)

# Shared properties
class UserBase(BaseModel):
    username: str
    email: EmailStr
    is_active: bool = True
    role: str = "annotator"

# Properties to receive on user creation
class UserCreate(UserBase):
    password: str

# Properties to receive on user update
class UserUpdate(BaseModel):
    username: Optional[str] = None
    email: Optional[EmailStr] = None
    password: Optional[str] = None
    is_active: Optional[bool] = None
    role: Optional[str] = None

# Base model for user in DB
class UserInDBBase(UserBase):
    model_config = ConfigDict(from_attributes=True)

    id: int
    created_at: datetime
    is_superuser: bool

# Properties to return to client
class User(UserSimple):
    owned_projects: List["ProjectSimple"] = []
    member_of_projects: List["ProjectSimple"] = []

# Properties stored in DB
class UserInDB(UserInDBBase):
    hashed_password: str

# To resolve the forward reference, we need to import Project here
from .project import ProjectSimple
User.update_forward_refs()
ProjectSimple.update_forward_refs() # It's safe to call it again here 