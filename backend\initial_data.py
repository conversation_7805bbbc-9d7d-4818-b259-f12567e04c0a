import asyncio
import logging
import os
from app.db.session import SessionLocal, engine
from app.schemas.user import UserCreate
from app.schemas.project import ProjectCreate  # Import ProjectCreate
from app import crud  # Import the entire crud module
from app.db import base  # noqa: F401
from sqlalchemy import text

# --- Start of new logging setup ---
log_file_path = os.path.join(os.path.dirname(__file__), 'backend_init.log')

# Clear log file if it exists
if os.path.exists(log_file_path):
    os.remove(log_file_path)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file_path),
        logging.StreamHandler() # Keep logging to console as well
    ]
)
# --- End of new logging setup ---

logger = logging.getLogger(__name__)

async def init_db(db: SessionLocal) -> None:
    # 使用CASCADE选项删除所有表
    async with engine.begin() as conn:
        # 先使用SQL直接执行DROP SCHEMA CASCADE来清理所有表
        await conn.execute(text("DROP SCHEMA public CASCADE"))
        await conn.execute(text("CREATE SCHEMA public"))
        # 然后创建所有表
        await conn.run_sync(base.Base.metadata.create_all)

    user = await crud.user.get_by_username(db, username="<EMAIL>")
    if not user:
        logger.info("Creating new admin user.")
        user_in = UserCreate(
            username="<EMAIL>",
            email="<EMAIL>",
            password="password",
            role="admin"
        )
        user = await crud.user.create(db, obj_in=user_in)
        logger.info(f"Created user: {user.username}")

    # 获取用户ID，避免在后续操作中访问已过期的对象属性
    user_id = user.id
    logger.info(f"Using user ID: {user_id} for project creation")

    # Create initial projects
    project_1 = await crud.project.get_project_by_name(db, name="ImageNet 2021 Classification")
    if not project_1:
        logger.info("Creating project: ImageNet 2021 Classification")
        project_1_in = ProjectCreate(
            name="ImageNet 2021 Classification",
            description="Classify images from the ImageNet 2021 dataset."
        )
        await crud.project.create_with_owner(db, obj_in=project_1_in, owner_id=user_id)
        logger.info("Project 'ImageNet 2021 Classification' created.")

    project_2 = await crud.project.get_project_by_name(db, name="COCO 2017 Object Detection")
    if not project_2:
        logger.info("Creating project: COCO 2017 Object Detection")
        project_2_in = ProjectCreate(
            name="COCO 2017 Object Detection",
            description="Detect objects in images from the COCO 2017 dataset."
        )
        await crud.project.create_with_owner(db, obj_in=project_2_in, owner_id=user_id)
        logger.info("Project 'COCO 2017 Object Detection' created.")

    # 创建更多示例项目
    project_3 = await crud.project.get_project_by_name(db, name="文本情感分析")
    if not project_3:
        logger.info("Creating project: 文本情感分析")
        project_3_in = ProjectCreate(
            name="文本情感分析",
            description="对中文文本进行情感分析标注，包括正面、负面和中性情感。"
        )
        await crud.project.create_with_owner(db, obj_in=project_3_in, owner_id=user_id)
        logger.info("Project '文本情感分析' created.")

    project_4 = await crud.project.get_project_by_name(db, name="医学图像分割")
    if not project_4:
        logger.info("Creating project: 医学图像分割")
        project_4_in = ProjectCreate(
            name="医学图像分割",
            description="对医学影像进行器官和病灶的精确分割标注。"
        )
        await crud.project.create_with_owner(db, obj_in=project_4_in, owner_id=user_id)
        logger.info("Project '医学图像分割' created.")


async def main() -> None:
    logger.info("Initializing service")
    try:
        async with SessionLocal() as db:
            await init_db(db)
        logger.info("Service finished initializing")
    except Exception as e:
        logger.error("An error occurred during service initialization.", exc_info=True)
        # Also print to stdout to be sure
        print(f"FATAL ERROR: {e}")
        raise

if __name__ == "__main__":
    try:
        logger.info("Starting main execution block.")
        asyncio.run(main())
        logger.info("Main execution block finished successfully.")
        print("--- SCRIPT FINISHED SUCCESSFULLY ---") # Add a clear success message
    except Exception as e:
        logger.error("An error occurred in the main execution block.", exc_info=True)
        print(f"FATAL ERROR in __main__: {e}")
        # Write to file one last time just in case
        with open(log_file_path, 'a') as f:
            f.write(f"\nFATAL ERROR in __main__: {e}\n") 