2025-06-21 16:15:43,677 - root - INFO - 应用启动中...
2025-06-21 16:15:43,868 - root - INFO - 创建数据库表...
2025-06-21 16:15:43,884 - root - INFO - 应用 '数据标注系统' 启动完成
2025-06-21 16:15:50,294 - root - INFO - 应用启动中...
2025-06-21 16:15:50,444 - root - INFO - 创建数据库表...
2025-06-21 16:15:50,453 - root - INFO - 应用 '数据标注系统' 启动完成
2025-06-21 16:16:29,658 - app.core.middleware - INFO - Request started: POST /api/v1/login
2025-06-21 16:16:29,661 - app.api.endpoints.login - INFO - 尝试登录用户: <EMAIL>
2025-06-21 16:16:29,672 - app.api.endpoints.login - ERROR - 登录过程中发生错误: Class 'sqlalchemy.sql.elements.Label' is not mapped
2025-06-21 16:16:29,672 - app.core.middleware - INFO - Request completed: POST /api/v1/login - Status: 500 - Duration: 0.0145s
2025-06-21 16:18:08,233 - root - INFO - 应用启动中...
2025-06-21 16:18:08,502 - root - INFO - 创建数据库表...
2025-06-21 16:18:08,510 - root - INFO - 应用 '数据标注系统' 启动完成
2025-06-21 16:18:26,996 - app.core.middleware - INFO - Request started: POST /api/v1/login
2025-06-21 16:18:26,996 - app.api.endpoints.login - INFO - 尝试登录用户: <EMAIL>
2025-06-21 16:18:27,014 - app.api.endpoints.login - ERROR - 登录过程中发生错误: Class 'sqlalchemy.sql.elements.Label' is not mapped
2025-06-21 16:18:27,015 - app.core.middleware - INFO - Request completed: POST /api/v1/login - Status: 500 - Duration: 0.0194s
2025-06-21 16:20:47,323 - root - INFO - 应用启动中...
2025-06-21 16:20:47,586 - root - INFO - 创建数据库表...
2025-06-21 16:20:47,596 - root - INFO - 应用 '数据标注系统' 启动完成
2025-06-21 16:21:25,989 - root - INFO - 应用启动中...
2025-06-21 16:21:26,134 - root - INFO - 创建数据库表...
2025-06-21 16:21:26,143 - root - INFO - 应用 '数据标注系统' 启动完成
2025-06-21 16:21:33,378 - app.core.middleware - INFO - Request started: POST /api/v1/login
2025-06-21 16:21:33,378 - app.api.endpoints.login - INFO - 尝试登录用户: <EMAIL>
2025-06-21 16:21:33,666 - app.api.endpoints.login - INFO - 用户登录成功: <EMAIL>
2025-06-21 16:21:33,668 - app.core.middleware - INFO - Request completed: POST /api/v1/login - Status: 200 - Duration: 0.2906s
2025-06-21 16:21:39,277 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 16:21:39,292 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0148s
2025-06-21 16:21:39,297 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 16:21:39,304 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0070s
2025-06-21 16:25:41,363 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 16:25:41,370 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0070s
2025-06-21 16:25:41,375 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 16:25:41,383 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0078s
2025-06-21 16:26:22,697 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 16:26:22,704 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0070s
2025-06-21 16:26:22,709 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 16:26:22,716 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0070s
2025-06-21 16:26:37,122 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 16:26:37,128 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0061s
2025-06-21 16:26:37,133 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 16:26:37,138 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0050s
2025-06-21 16:32:27,755 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 16:32:27,762 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0070s
2025-06-21 16:32:27,769 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 16:32:27,776 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0070s
2025-06-21 16:32:46,185 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 16:32:46,190 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0050s
2025-06-21 16:32:46,195 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 16:32:46,201 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0060s
2025-06-21 16:35:23,013 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 16:35:23,019 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0060s
2025-06-21 16:35:23,025 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 16:35:23,031 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0060s
2025-06-21 16:35:23,758 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 16:35:23,765 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0063s
2025-06-21 16:35:23,772 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 16:35:23,778 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0060s
2025-06-21 16:35:26,654 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 16:35:26,660 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0060s
2025-06-21 16:35:26,667 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 16:35:26,673 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0073s
2025-06-21 16:36:33,791 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 16:36:33,795 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0040s
2025-06-21 16:36:33,800 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 16:36:33,805 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0050s
2025-06-21 16:38:21,236 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 16:38:21,242 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0060s
2025-06-21 16:38:21,247 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 16:38:21,252 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0050s
2025-06-21 16:38:25,161 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 16:38:25,162 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0016s
2025-06-21 16:38:25,171 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 16:38:25,175 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0046s
2025-06-21 16:39:29,525 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 16:39:29,530 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0054s
2025-06-21 16:39:29,536 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 16:39:29,542 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0060s
2025-06-21 16:40:08,797 - root - INFO - 应用关闭中...
2025-06-21 16:40:08,797 - root - INFO - 应用关闭中...
2025-06-21 16:40:08,801 - root - INFO - 应用已关闭
2025-06-21 16:40:08,801 - root - INFO - 应用已关闭
2025-06-21 16:40:10,270 - root - INFO - 应用启动中...
2025-06-21 16:40:10,271 - root - INFO - 应用启动中...
2025-06-21 16:40:10,460 - root - INFO - 创建数据库表...
2025-06-21 16:40:10,475 - root - INFO - 应用 '数据标注系统' 启动完成
2025-06-21 16:40:10,560 - root - INFO - 创建数据库表...
2025-06-21 16:40:10,580 - root - INFO - 应用 '数据标注系统' 启动完成
2025-06-21 16:41:52,184 - root - INFO - 应用关闭中...
2025-06-21 16:41:52,184 - root - INFO - 应用已关闭
2025-06-21 16:41:52,201 - root - INFO - 应用关闭中...
2025-06-21 16:41:52,204 - root - INFO - 应用已关闭
2025-06-21 16:41:53,478 - root - INFO - 应用启动中...
2025-06-21 16:41:53,496 - root - INFO - 应用启动中...
2025-06-21 16:41:53,672 - root - INFO - 创建数据库表...
2025-06-21 16:41:53,683 - root - INFO - 应用 '数据标注系统' 启动完成
2025-06-21 16:41:53,766 - root - INFO - 创建数据库表...
2025-06-21 16:41:53,775 - root - INFO - 应用 '数据标注系统' 启动完成
2025-06-21 16:42:14,718 - root - INFO - 应用关闭中...
2025-06-21 16:42:14,718 - root - INFO - 应用关闭中...
2025-06-21 16:42:14,721 - root - INFO - 应用已关闭
2025-06-21 16:42:14,721 - root - INFO - 应用已关闭
2025-06-21 16:42:16,238 - root - INFO - 应用启动中...
2025-06-21 16:42:16,238 - root - INFO - 应用启动中...
2025-06-21 16:42:16,429 - root - INFO - 创建数据库表...
2025-06-21 16:42:16,441 - root - INFO - 应用 '数据标注系统' 启动完成
2025-06-21 16:42:16,542 - root - INFO - 创建数据库表...
2025-06-21 16:42:16,557 - root - INFO - 应用 '数据标注系统' 启动完成
2025-06-21 16:42:53,440 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 16:42:53,448 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 401 - Duration: 0.0079s
2025-06-21 16:42:53,454 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 16:42:53,457 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 401 - Duration: 0.0030s
2025-06-21 16:43:04,717 - app.core.middleware - INFO - Request started: POST /api/v1/login
2025-06-21 16:43:04,720 - app.api.endpoints.login - INFO - 尝试登录用户:  <EMAIL>
2025-06-21 16:43:04,725 - app.api.endpoints.login - WARNING - 用户不存在:  <EMAIL>
2025-06-21 16:43:04,725 - app.core.middleware - INFO - Request completed: POST /api/v1/login - Status: 400 - Duration: 0.0086s
2025-06-21 16:43:08,439 - app.core.middleware - INFO - Request started: POST /api/v1/login
2025-06-21 16:43:08,441 - app.api.endpoints.login - INFO - 尝试登录用户: <EMAIL>
2025-06-21 16:43:08,689 - app.api.endpoints.login - INFO - 用户登录成功: <EMAIL>
2025-06-21 16:43:08,691 - app.core.middleware - INFO - Request completed: POST /api/v1/login - Status: 200 - Duration: 0.2522s
2025-06-21 16:43:11,338 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 16:43:11,348 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0090s
2025-06-21 16:43:11,353 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 16:43:11,360 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0066s
2025-06-21 16:43:28,880 - root - INFO - 应用关闭中...
2025-06-21 16:43:28,882 - root - INFO - 应用已关闭
2025-06-21 16:46:21,268 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 16:46:21,274 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 401 - Duration: 0.0060s
2025-06-21 16:46:21,281 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 16:46:21,283 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 401 - Duration: 0.0020s
2025-06-21 16:46:32,715 - app.core.middleware - INFO - Request started: POST /api/v1/login
2025-06-21 16:46:32,717 - app.api.endpoints.login - INFO - 尝试登录用户: <EMAIL>
2025-06-21 16:46:32,979 - app.api.endpoints.login - INFO - 用户登录成功: <EMAIL>
2025-06-21 16:46:32,980 - app.core.middleware - INFO - Request completed: POST /api/v1/login - Status: 200 - Duration: 0.2649s
2025-06-21 16:46:35,263 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 16:46:35,277 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0140s
2025-06-21 16:46:35,283 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 16:46:35,292 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0085s
2025-06-21 16:47:42,121 - root - INFO - 应用启动中...
2025-06-21 16:47:42,265 - root - INFO - 创建数据库表...
2025-06-21 16:47:42,277 - root - INFO - 应用 '数据标注系统' 启动完成
2025-06-21 16:47:46,909 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 16:47:46,915 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0060s
2025-06-21 16:47:46,921 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 16:47:46,927 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0060s
2025-06-21 16:49:20,161 - root - INFO - 应用关闭中...
2025-06-21 16:49:20,161 - root - INFO - 应用关闭中...
2025-06-21 16:49:20,164 - root - INFO - 应用已关闭
2025-06-21 16:49:20,164 - root - INFO - 应用已关闭
2025-06-21 16:49:21,482 - root - INFO - 应用启动中...
2025-06-21 16:49:21,699 - root - INFO - 创建数据库表...
2025-06-21 16:49:21,710 - root - INFO - 应用 '数据标注系统' 启动完成
2025-06-21 16:49:21,738 - root - INFO - 应用启动中...
2025-06-21 16:49:21,925 - root - INFO - 创建数据库表...
2025-06-21 16:49:21,929 - root - INFO - 应用 '数据标注系统' 启动完成
2025-06-21 16:49:58,398 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 16:49:58,403 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 401 - Duration: 0.0050s
2025-06-21 16:49:58,407 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 16:49:58,409 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 401 - Duration: 0.0020s
2025-06-21 16:50:06,504 - app.core.middleware - INFO - Request started: POST /api/v1/login
2025-06-21 16:50:06,506 - app.api.endpoints.login - INFO - 尝试登录用户: <EMAIL>
2025-06-21 16:50:06,761 - app.api.endpoints.login - INFO - 用户登录成功: <EMAIL>
2025-06-21 16:50:06,761 - app.core.middleware - INFO - Request completed: POST /api/v1/login - Status: 200 - Duration: 0.2570s
2025-06-21 16:50:08,832 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 16:50:08,843 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0103s
2025-06-21 16:50:08,849 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 16:50:08,854 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0054s
2025-06-21 16:56:59,419 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 16:56:59,426 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0070s
2025-06-21 16:56:59,433 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 16:56:59,438 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0060s
2025-06-21 16:57:40,437 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 16:57:40,437 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0000s
2025-06-21 16:57:40,448 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 16:57:40,455 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0067s
2025-06-21 16:58:31,636 - root - INFO - 应用关闭中...
2025-06-21 16:58:31,636 - root - INFO - 应用关闭中...
2025-06-21 16:58:31,639 - root - INFO - 应用已关闭
2025-06-21 16:58:32,870 - root - INFO - 应用启动中...
2025-06-21 16:58:33,014 - root - INFO - 创建数据库表...
2025-06-21 16:58:33,022 - root - INFO - 应用 '数据标注系统' 启动完成
2025-06-21 16:58:33,079 - root - INFO - 应用启动中...
2025-06-21 16:58:33,220 - root - INFO - 创建数据库表...
2025-06-21 16:58:33,239 - root - INFO - 应用 '数据标注系统' 启动完成
2025-06-21 17:00:12,102 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:00:12,102 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 307 - Duration: 0.0000s
2025-06-21 17:00:12,109 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:00:12,110 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 307 - Duration: 0.0010s
2025-06-21 17:00:12,123 - app.core.middleware - INFO - Request started: GET /api/v1/projects
2025-06-21 17:00:12,126 - app.core.middleware - INFO - Request completed: GET /api/v1/projects - Status: 401 - Duration: 0.0022s
2025-06-21 17:00:12,128 - app.core.middleware - INFO - Request started: GET /api/v1/projects
2025-06-21 17:00:12,129 - app.core.middleware - INFO - Request completed: GET /api/v1/projects - Status: 401 - Duration: 0.0010s
2025-06-21 17:00:24,876 - app.core.middleware - INFO - Request started: POST /api/v1/login
2025-06-21 17:00:24,879 - app.api.endpoints.login - INFO - 尝试登录用户: <EMAIL>
2025-06-21 17:00:25,135 - app.api.endpoints.login - INFO - 用户登录成功: <EMAIL>
2025-06-21 17:00:25,135 - app.core.middleware - INFO - Request completed: POST /api/v1/login - Status: 200 - Duration: 0.2581s
2025-06-21 17:00:27,552 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:00:27,553 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 307 - Duration: 0.0010s
2025-06-21 17:00:27,558 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:00:27,559 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 307 - Duration: 0.0010s
2025-06-21 17:00:27,566 - app.core.middleware - INFO - Request started: GET /api/v1/projects
2025-06-21 17:00:27,568 - app.core.middleware - INFO - Request completed: GET /api/v1/projects - Status: 401 - Duration: 0.0020s
2025-06-21 17:00:27,571 - app.core.middleware - INFO - Request started: GET /api/v1/projects
2025-06-21 17:00:27,572 - app.core.middleware - INFO - Request completed: GET /api/v1/projects - Status: 401 - Duration: 0.0010s
2025-06-21 17:00:36,931 - app.core.middleware - INFO - Request started: POST /api/v1/login
2025-06-21 17:00:36,933 - app.api.endpoints.login - INFO - 尝试登录用户:  <EMAIL>
2025-06-21 17:00:36,936 - app.api.endpoints.login - WARNING - 用户不存在:  <EMAIL>
2025-06-21 17:00:36,937 - app.core.middleware - INFO - Request completed: POST /api/v1/login - Status: 400 - Duration: 0.0060s
2025-06-21 17:00:46,941 - app.core.middleware - INFO - Request started: POST /api/v1/login
2025-06-21 17:00:46,943 - app.api.endpoints.login - INFO - 尝试登录用户: <EMAIL>
2025-06-21 17:00:47,167 - app.api.endpoints.login - INFO - 用户登录成功: <EMAIL>
2025-06-21 17:00:47,169 - app.core.middleware - INFO - Request completed: POST /api/v1/login - Status: 200 - Duration: 0.2279s
2025-06-21 17:00:50,240 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:00:50,241 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 307 - Duration: 0.0010s
2025-06-21 17:00:50,246 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:00:50,247 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 307 - Duration: 0.0010s
2025-06-21 17:00:50,257 - app.core.middleware - INFO - Request started: GET /api/v1/projects
2025-06-21 17:00:50,258 - app.core.middleware - INFO - Request completed: GET /api/v1/projects - Status: 401 - Duration: 0.0010s
2025-06-21 17:00:50,261 - app.core.middleware - INFO - Request started: GET /api/v1/projects
2025-06-21 17:00:50,263 - app.core.middleware - INFO - Request completed: GET /api/v1/projects - Status: 401 - Duration: 0.0020s
2025-06-21 17:01:18,186 - app.core.middleware - INFO - Request started: POST /api/v1/login
2025-06-21 17:01:18,188 - app.api.endpoints.login - INFO - 尝试登录用户: <EMAIL>
2025-06-21 17:01:18,402 - app.api.endpoints.login - INFO - 用户登录成功: <EMAIL>
2025-06-21 17:01:18,402 - app.core.middleware - INFO - Request completed: POST /api/v1/login - Status: 200 - Duration: 0.2164s
2025-06-21 17:01:20,296 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:01:20,297 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 307 - Duration: 0.0010s
2025-06-21 17:01:20,303 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:01:20,304 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 307 - Duration: 0.0010s
2025-06-21 17:01:20,311 - app.core.middleware - INFO - Request started: GET /api/v1/projects
2025-06-21 17:01:20,313 - app.core.middleware - INFO - Request completed: GET /api/v1/projects - Status: 401 - Duration: 0.0021s
2025-06-21 17:01:20,316 - app.core.middleware - INFO - Request started: GET /api/v1/projects
2025-06-21 17:01:20,318 - app.core.middleware - INFO - Request completed: GET /api/v1/projects - Status: 401 - Duration: 0.0020s
2025-06-21 17:03:29,229 - root - INFO - 应用关闭中...
2025-06-21 17:03:29,233 - root - INFO - 应用已关闭
2025-06-21 17:03:29,331 - root - INFO - 应用关闭中...
2025-06-21 17:03:29,335 - root - INFO - 应用已关闭
2025-06-21 17:03:30,529 - root - INFO - 应用启动中...
2025-06-21 17:03:30,672 - root - INFO - 创建数据库表...
2025-06-21 17:03:30,682 - root - INFO - 应用 '数据标注系统' 启动完成
2025-06-21 17:03:30,810 - root - INFO - 应用启动中...
2025-06-21 17:03:30,954 - root - INFO - 创建数据库表...
2025-06-21 17:03:30,962 - root - INFO - 应用 '数据标注系统' 启动完成
2025-06-21 17:04:09,893 - app.core.middleware - INFO - Request started: POST /api/v1/login
2025-06-21 17:04:09,900 - app.api.endpoints.login - INFO - 尝试登录用户: <EMAIL>
2025-06-21 17:04:10,152 - app.api.endpoints.login - INFO - 用户登录成功: <EMAIL>
2025-06-21 17:04:10,152 - app.core.middleware - INFO - Request completed: POST /api/v1/login - Status: 200 - Duration: 0.2587s
2025-06-21 17:04:12,265 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:04:12,276 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0110s
2025-06-21 17:04:12,282 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:04:12,287 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0060s
2025-06-21 17:04:15,728 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:04:15,733 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0058s
2025-06-21 17:04:15,739 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:04:15,744 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0060s
2025-06-21 17:07:44,463 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:07:44,470 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0067s
2025-06-21 17:07:44,475 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:07:44,481 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0060s
2025-06-21 17:08:53,120 - root - INFO - 应用关闭中...
2025-06-21 17:08:53,121 - root - INFO - 应用关闭中...
2025-06-21 17:08:53,124 - root - INFO - 应用已关闭
2025-06-21 17:08:53,125 - root - INFO - 应用已关闭
2025-06-21 17:08:54,427 - root - INFO - 应用启动中...
2025-06-21 17:08:54,610 - root - INFO - 创建数据库表...
2025-06-21 17:08:54,619 - root - INFO - 应用 '数据标注系统' 启动完成
2025-06-21 17:08:54,662 - root - INFO - 应用启动中...
2025-06-21 17:08:54,836 - root - INFO - 创建数据库表...
2025-06-21 17:08:54,846 - root - INFO - 应用 '数据标注系统' 启动完成
2025-06-21 17:10:05,503 - root - INFO - 应用关闭中...
2025-06-21 17:10:05,503 - root - INFO - 应用关闭中...
2025-06-21 17:10:05,507 - root - INFO - 应用已关闭
2025-06-21 17:10:05,506 - root - INFO - 应用已关闭
2025-06-21 17:10:07,177 - root - INFO - 应用启动中...
2025-06-21 17:10:07,511 - root - INFO - 创建数据库表...
2025-06-21 17:10:07,525 - root - INFO - 应用 '数据标注系统' 启动完成
2025-06-21 17:10:07,548 - root - INFO - 应用启动中...
2025-06-21 17:10:07,723 - root - INFO - 创建数据库表...
2025-06-21 17:10:07,734 - root - INFO - 应用 '数据标注系统' 启动完成
2025-06-21 17:12:18,166 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:12:18,172 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 401 - Duration: 0.0060s
2025-06-21 17:12:18,179 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:12:18,182 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 401 - Duration: 0.0030s
2025-06-21 17:12:28,670 - app.core.middleware - INFO - Request started: POST /api/v1/login
2025-06-21 17:12:28,670 - app.api.endpoints.login - INFO - 尝试登录用户: <EMAIL>
2025-06-21 17:12:28,938 - app.api.endpoints.login - INFO - 用户登录成功: <EMAIL>
2025-06-21 17:12:28,938 - app.core.middleware - INFO - Request completed: POST /api/v1/login - Status: 200 - Duration: 0.2677s
2025-06-21 17:12:30,929 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:12:30,940 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0100s
2025-06-21 17:12:30,945 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:12:30,951 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0060s
2025-06-21 17:13:06,483 - root - INFO - 应用关闭中...
2025-06-21 17:13:06,487 - root - INFO - 应用已关闭
2025-06-21 17:13:06,597 - root - INFO - 应用关闭中...
2025-06-21 17:13:06,600 - root - INFO - 应用已关闭
2025-06-21 17:13:07,754 - root - INFO - 应用启动中...
2025-06-21 17:13:07,899 - root - INFO - 创建数据库表...
2025-06-21 17:13:07,908 - root - INFO - 应用 '数据标注系统' 启动完成
2025-06-21 17:13:08,069 - root - INFO - 应用启动中...
2025-06-21 17:13:08,214 - root - INFO - 创建数据库表...
2025-06-21 17:13:08,223 - root - INFO - 应用 '数据标注系统' 启动完成
2025-06-21 17:13:43,722 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:13:43,727 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 401 - Duration: 0.0050s
2025-06-21 17:13:43,733 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:13:43,736 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 401 - Duration: 0.0030s
2025-06-21 17:13:51,788 - app.core.middleware - INFO - Request started: POST /api/v1/login
2025-06-21 17:13:51,789 - app.api.endpoints.login - INFO - 尝试登录用户: <EMAIL>
2025-06-21 17:13:52,047 - app.api.endpoints.login - INFO - 用户登录成功: <EMAIL>
2025-06-21 17:13:52,047 - app.core.middleware - INFO - Request completed: POST /api/v1/login - Status: 200 - Duration: 0.2588s
2025-06-21 17:13:53,938 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:13:53,938 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0000s
2025-06-21 17:13:53,954 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:13:53,959 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0058s
2025-06-21 17:15:36,689 - app.core.middleware - INFO - Request started: POST /api/v1/login
2025-06-21 17:15:36,691 - app.api.endpoints.login - INFO - 尝试登录用户: <EMAIL>
2025-06-21 17:15:36,916 - app.api.endpoints.login - INFO - 用户登录成功: <EMAIL>
2025-06-21 17:15:36,919 - app.core.middleware - INFO - Request completed: POST /api/v1/login - Status: 200 - Duration: 0.2303s
2025-06-21 17:15:38,584 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:15:38,590 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0063s
2025-06-21 17:15:38,594 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:15:38,600 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0060s
2025-06-21 17:16:12,174 - root - INFO - 应用启动中...
2025-06-21 17:16:12,323 - root - INFO - 创建数据库表...
2025-06-21 17:16:12,333 - root - INFO - 应用 '数据标注系统' 启动完成
2025-06-21 17:16:57,994 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:16:58,001 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0070s
2025-06-21 17:16:58,006 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:16:58,013 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0070s
2025-06-21 17:17:01,209 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:17:01,215 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0060s
2025-06-21 17:17:01,220 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:17:01,226 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0052s
2025-06-21 17:19:37,739 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:19:37,745 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0037s
2025-06-21 17:19:37,750 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:19:37,756 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0060s
2025-06-21 17:19:41,305 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:19:41,311 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0060s
2025-06-21 17:19:41,316 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:19:41,321 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0059s
2025-06-21 17:23:18,336 - root - INFO - 应用启动中...
2025-06-21 17:23:18,481 - root - INFO - 创建数据库表...
2025-06-21 17:23:18,490 - root - INFO - 应用 '数据标注系统' 启动完成
2025-06-21 17:23:18,492 - root - INFO - 应用关闭中...
2025-06-21 17:23:18,495 - root - INFO - 应用已关闭
2025-06-21 17:24:03,292 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:24:03,298 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0062s
2025-06-21 17:24:03,304 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:24:03,309 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0040s
2025-06-21 17:24:52,392 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:24:52,397 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0050s
2025-06-21 17:24:52,402 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:24:52,407 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0050s
2025-06-21 17:24:53,707 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:24:53,708 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0008s
2025-06-21 17:24:53,718 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:24:53,723 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0060s
2025-06-21 17:24:54,956 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:24:54,962 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0050s
2025-06-21 17:24:54,968 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:24:54,973 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0052s
2025-06-21 17:26:20,864 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:26:20,866 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0023s
2025-06-21 17:26:20,874 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:26:20,881 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0070s
2025-06-21 17:26:32,845 - app.core.middleware - INFO - Request started: POST /api/v1/login
2025-06-21 17:26:32,847 - app.api.endpoints.login - INFO - 尝试登录用户: <EMAIL>
2025-06-21 17:26:33,075 - app.api.endpoints.login - INFO - 用户登录成功: <EMAIL>
2025-06-21 17:26:33,078 - app.core.middleware - INFO - Request completed: POST /api/v1/login - Status: 200 - Duration: 0.2334s
2025-06-21 17:26:35,124 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:26:35,124 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0004s
2025-06-21 17:26:35,134 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:26:35,139 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0050s
2025-06-21 17:27:39,028 - root - INFO - 应用启动中...
2025-06-21 17:27:39,206 - root - INFO - 创建数据库表...
2025-06-21 17:27:39,215 - root - INFO - 应用 '数据标注系统' 启动完成
2025-06-21 17:27:39,217 - root - INFO - 应用关闭中...
2025-06-21 17:27:39,219 - root - INFO - 应用已关闭
2025-06-21 17:31:23,848 - root - INFO - 应用启动中...
2025-06-21 17:31:24,000 - root - INFO - 创建数据库表...
2025-06-21 17:31:24,009 - root - INFO - 应用 '数据标注系统' 启动完成
2025-06-21 17:31:24,012 - root - INFO - 应用关闭中...
2025-06-21 17:31:24,015 - root - INFO - 应用已关闭
2025-06-21 17:31:31,662 - root - INFO - 应用启动中...
2025-06-21 17:31:31,810 - root - INFO - 创建数据库表...
2025-06-21 17:31:31,810 - root - INFO - 应用 '数据标注系统' 启动完成
2025-06-21 17:31:57,671 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:31:57,678 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0070s
2025-06-21 17:31:57,684 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:31:57,689 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0050s
2025-06-21 17:31:59,088 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:31:59,094 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0060s
2025-06-21 17:31:59,099 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:31:59,108 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0089s
2025-06-21 17:38:40,784 - root - INFO - 应用启动中...
2025-06-21 17:38:40,928 - root - INFO - 创建数据库表...
2025-06-21 17:38:40,932 - root - INFO - 应用 '数据标注系统' 启动完成
2025-06-21 17:39:43,448 - root - INFO - 应用启动中...
2025-06-21 17:39:43,580 - root - INFO - 创建数据库表...
2025-06-21 17:39:43,597 - root - INFO - 应用 '数据标注系统' 启动完成
2025-06-21 17:40:14,654 - root - INFO - 应用启动中...
2025-06-21 17:40:14,902 - root - INFO - 创建数据库表...
2025-06-21 17:40:14,916 - root - INFO - 应用 '数据标注系统' 启动完成
2025-06-21 17:41:20,647 - root - INFO - 应用启动中...
2025-06-21 17:41:20,785 - root - INFO - 创建数据库表...
2025-06-21 17:41:20,800 - root - INFO - 应用 '数据标注系统' 启动完成
2025-06-21 17:41:55,525 - root - INFO - 应用启动中...
2025-06-21 17:41:55,670 - root - INFO - 创建数据库表...
2025-06-21 17:41:55,678 - root - INFO - 应用 '数据标注系统' 启动完成
2025-06-21 17:41:55,680 - root - INFO - 应用关闭中...
2025-06-21 17:41:55,683 - root - INFO - 应用已关闭
2025-06-21 17:42:23,654 - root - INFO - 应用启动中...
2025-06-21 17:42:23,800 - root - INFO - 创建数据库表...
2025-06-21 17:42:23,808 - root - INFO - 应用 '数据标注系统' 启动完成
2025-06-21 17:44:11,792 - root - INFO - 应用关闭中...
2025-06-21 17:44:11,792 - root - INFO - 应用关闭中...
2025-06-21 17:44:11,792 - root - INFO - 应用已关闭
2025-06-21 17:44:11,792 - root - INFO - 应用已关闭
2025-06-21 17:44:13,448 - root - INFO - 应用启动中...
2025-06-21 17:44:13,489 - root - INFO - 应用启动中...
2025-06-21 17:44:13,632 - root - INFO - 创建数据库表...
2025-06-21 17:44:13,642 - root - INFO - 应用 '数据标注系统' 启动完成
2025-06-21 17:44:13,707 - root - INFO - 创建数据库表...
2025-06-21 17:44:13,707 - root - INFO - 应用 '数据标注系统' 启动完成
2025-06-21 17:45:58,189 - root - INFO - 应用启动中...
2025-06-21 17:45:58,328 - root - INFO - 创建数据库表...
2025-06-21 17:45:58,341 - root - INFO - 应用 '数据标注系统' 启动完成
2025-06-21 17:46:22,731 - root - INFO - 应用启动中...
2025-06-21 17:46:22,875 - root - INFO - 创建数据库表...
2025-06-21 17:46:22,885 - root - INFO - 应用 '数据标注系统' 启动完成
2025-06-21 17:46:22,886 - root - INFO - 应用关闭中...
2025-06-21 17:46:22,888 - root - INFO - 应用已关闭
2025-06-21 17:46:59,513 - root - INFO - 应用启动中...
2025-06-21 17:46:59,656 - root - INFO - 创建数据库表...
2025-06-21 17:46:59,675 - root - INFO - 应用 '数据标注系统' 启动完成
2025-06-21 17:48:05,986 - root - INFO - 应用启动中...
2025-06-21 17:48:06,132 - root - INFO - 创建数据库表...
2025-06-21 17:48:06,136 - root - INFO - 应用 '数据标注系统' 启动完成
2025-06-21 17:48:06,136 - root - INFO - 应用关闭中...
2025-06-21 17:48:06,136 - root - INFO - 应用已关闭
2025-06-21 17:48:40,084 - root - INFO - 应用启动中...
2025-06-21 17:48:40,225 - root - INFO - 创建数据库表...
2025-06-21 17:48:40,225 - root - INFO - 应用 '数据标注系统' 启动完成
2025-06-21 17:48:40,225 - root - INFO - 应用关闭中...
2025-06-21 17:48:40,243 - root - INFO - 应用已关闭
2025-06-21 17:49:00,434 - root - INFO - 应用启动中...
2025-06-21 17:49:00,580 - root - INFO - 创建数据库表...
2025-06-21 17:49:00,590 - root - INFO - 应用 '数据标注系统' 启动完成
2025-06-21 17:49:00,592 - root - INFO - 应用关闭中...
2025-06-21 17:49:00,595 - root - INFO - 应用已关闭
2025-06-21 17:50:07,074 - root - INFO - 应用启动中...
2025-06-21 17:50:07,220 - root - INFO - 创建数据库表...
2025-06-21 17:50:07,229 - root - INFO - 应用 '数据标注系统' 启动完成
2025-06-21 17:50:07,231 - root - INFO - 应用关闭中...
2025-06-21 17:50:07,235 - root - INFO - 应用已关闭
2025-06-21 17:50:48,045 - app.core.middleware - INFO - Request started: GET /docs
2025-06-21 17:50:48,045 - app.core.middleware - INFO - Request completed: GET /docs - Status: 200 - Duration: 0.0000s
2025-06-21 17:51:03,951 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:51:03,952 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 401 - Duration: 0.0010s
2025-06-21 17:52:29,248 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:52:29,274 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 401 - Duration: 0.0262s
2025-06-21 17:52:29,286 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:52:29,288 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 401 - Duration: 0.0020s
2025-06-21 17:52:50,310 - app.core.middleware - INFO - Request started: POST /api/v1/login
2025-06-21 17:52:50,312 - app.api.endpoints.login - INFO - 尝试登录用户: <EMAIL>
2025-06-21 17:52:50,550 - app.api.endpoints.login - INFO - 用户登录成功: <EMAIL>
2025-06-21 17:52:50,556 - app.core.middleware - INFO - Request completed: POST /api/v1/login - Status: 200 - Duration: 0.2456s
2025-06-21 17:52:52,392 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:52:52,421 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0293s
2025-06-21 17:52:52,427 - app.core.middleware - INFO - Request started: GET /api/v1/projects/
2025-06-21 17:52:52,437 - app.core.middleware - INFO - Request completed: GET /api/v1/projects/ - Status: 200 - Duration: 0.0104s
2025-06-21 17:53:05,259 - app.core.middleware - INFO - Request started: POST /api/v1/projects/
2025-06-21 17:53:05,278 - app.core.middleware - ERROR - Request failed: POST /api/v1/projects/ - Error: 2 validation errors:
  {'type': 'get_attribute_error', 'loc': ('response', 'owner'), 'msg': "Error extracting attribute: MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)", 'input': <app.models.project.Project object at 0x000002185CE2BD50>, 'ctx': {'error': "MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)"}}
  {'type': 'get_attribute_error', 'loc': ('response', 'members'), 'msg': "Error extracting attribute: MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)", 'input': <app.models.project.Project object at 0x000002185CE2BD50>, 'ctx': {'error': "MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)"}}
 - Duration: 0.0187s
Traceback (most recent call last):
  File "F:\corsor\test34\backend\venv\Lib\site-packages\anyio\streams\memory.py", line 97, in receive
    return self.receive_nowait()
           ^^^^^^^^^^^^^^^^^^^^^
  File "F:\corsor\test34\backend\venv\Lib\site-packages\anyio\streams\memory.py", line 92, in receive_nowait
    raise WouldBlock
anyio.WouldBlock

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\corsor\test34\backend\venv\Lib\site-packages\anyio\streams\memory.py", line 112, in receive
    raise EndOfStream
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\corsor\test34\backend\app\core\middleware.py", line 25, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\middleware\base.py", line 165, in call_next
    raise app_exc
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\middleware\base.py", line 151, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\middleware\cors.py", line 148, in simple_response
    await self.app(scope, receive, send)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\middleware\exceptions.py", line 65, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\_exception_handler.py", line 64, in wrapped_app
    raise exc
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\routing.py", line 756, in __call__
    await self.middleware_stack(scope, receive, send)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\routing.py", line 776, in app
    await route.handle(scope, receive, send)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\routing.py", line 297, in handle
    await self.app(scope, receive, send)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\routing.py", line 77, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\_exception_handler.py", line 64, in wrapped_app
    raise exc
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\routing.py", line 72, in app
    response = await func(request)
               ^^^^^^^^^^^^^^^^^^^
  File "F:\corsor\test34\backend\venv\Lib\site-packages\fastapi\routing.py", line 296, in app
    content = await serialize_response(
              ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\corsor\test34\backend\venv\Lib\site-packages\fastapi\routing.py", line 155, in serialize_response
    raise ResponseValidationError(
fastapi.exceptions.ResponseValidationError: 2 validation errors:
  {'type': 'get_attribute_error', 'loc': ('response', 'owner'), 'msg': "Error extracting attribute: MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)", 'input': <app.models.project.Project object at 0x000002185CE2BD50>, 'ctx': {'error': "MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)"}}
  {'type': 'get_attribute_error', 'loc': ('response', 'members'), 'msg': "Error extracting attribute: MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)", 'input': <app.models.project.Project object at 0x000002185CE2BD50>, 'ctx': {'error': "MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)"}}

2025-06-21 17:53:06,788 - app.core.middleware - INFO - Request started: POST /api/v1/projects/
2025-06-21 17:53:06,788 - app.core.middleware - ERROR - Request failed: POST /api/v1/projects/ - Error: 2 validation errors:
  {'type': 'get_attribute_error', 'loc': ('response', 'owner'), 'msg': "Error extracting attribute: MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)", 'input': <app.models.project.Project object at 0x000002185D259110>, 'ctx': {'error': "MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)"}}
  {'type': 'get_attribute_error', 'loc': ('response', 'members'), 'msg': "Error extracting attribute: MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)", 'input': <app.models.project.Project object at 0x000002185D259110>, 'ctx': {'error': "MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)"}}
 - Duration: 0.0000s
Traceback (most recent call last):
  File "F:\corsor\test34\backend\venv\Lib\site-packages\anyio\streams\memory.py", line 97, in receive
    return self.receive_nowait()
           ^^^^^^^^^^^^^^^^^^^^^
  File "F:\corsor\test34\backend\venv\Lib\site-packages\anyio\streams\memory.py", line 92, in receive_nowait
    raise WouldBlock
anyio.WouldBlock

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\corsor\test34\backend\venv\Lib\site-packages\anyio\streams\memory.py", line 112, in receive
    raise EndOfStream
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\corsor\test34\backend\app\core\middleware.py", line 25, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\middleware\base.py", line 165, in call_next
    raise app_exc
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\middleware\base.py", line 151, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\middleware\cors.py", line 148, in simple_response
    await self.app(scope, receive, send)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\middleware\exceptions.py", line 65, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\_exception_handler.py", line 64, in wrapped_app
    raise exc
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\routing.py", line 756, in __call__
    await self.middleware_stack(scope, receive, send)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\routing.py", line 776, in app
    await route.handle(scope, receive, send)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\routing.py", line 297, in handle
    await self.app(scope, receive, send)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\routing.py", line 77, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\_exception_handler.py", line 64, in wrapped_app
    raise exc
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\routing.py", line 72, in app
    response = await func(request)
               ^^^^^^^^^^^^^^^^^^^
  File "F:\corsor\test34\backend\venv\Lib\site-packages\fastapi\routing.py", line 296, in app
    content = await serialize_response(
              ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\corsor\test34\backend\venv\Lib\site-packages\fastapi\routing.py", line 155, in serialize_response
    raise ResponseValidationError(
fastapi.exceptions.ResponseValidationError: 2 validation errors:
  {'type': 'get_attribute_error', 'loc': ('response', 'owner'), 'msg': "Error extracting attribute: MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)", 'input': <app.models.project.Project object at 0x000002185D259110>, 'ctx': {'error': "MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)"}}
  {'type': 'get_attribute_error', 'loc': ('response', 'members'), 'msg': "Error extracting attribute: MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)", 'input': <app.models.project.Project object at 0x000002185D259110>, 'ctx': {'error': "MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)"}}

2025-06-21 17:53:07,147 - app.core.middleware - INFO - Request started: POST /api/v1/projects/
2025-06-21 17:53:07,153 - app.core.middleware - ERROR - Request failed: POST /api/v1/projects/ - Error: 2 validation errors:
  {'type': 'get_attribute_error', 'loc': ('response', 'owner'), 'msg': "Error extracting attribute: MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)", 'input': <app.models.project.Project object at 0x000002185D1F7410>, 'ctx': {'error': "MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)"}}
  {'type': 'get_attribute_error', 'loc': ('response', 'members'), 'msg': "Error extracting attribute: MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)", 'input': <app.models.project.Project object at 0x000002185D1F7410>, 'ctx': {'error': "MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)"}}
 - Duration: 0.0062s
Traceback (most recent call last):
  File "F:\corsor\test34\backend\venv\Lib\site-packages\anyio\streams\memory.py", line 97, in receive
    return self.receive_nowait()
           ^^^^^^^^^^^^^^^^^^^^^
  File "F:\corsor\test34\backend\venv\Lib\site-packages\anyio\streams\memory.py", line 92, in receive_nowait
    raise WouldBlock
anyio.WouldBlock

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\corsor\test34\backend\venv\Lib\site-packages\anyio\streams\memory.py", line 112, in receive
    raise EndOfStream
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\corsor\test34\backend\app\core\middleware.py", line 25, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\middleware\base.py", line 165, in call_next
    raise app_exc
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\middleware\base.py", line 151, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\middleware\cors.py", line 148, in simple_response
    await self.app(scope, receive, send)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\middleware\exceptions.py", line 65, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\_exception_handler.py", line 64, in wrapped_app
    raise exc
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\routing.py", line 756, in __call__
    await self.middleware_stack(scope, receive, send)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\routing.py", line 776, in app
    await route.handle(scope, receive, send)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\routing.py", line 297, in handle
    await self.app(scope, receive, send)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\routing.py", line 77, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\_exception_handler.py", line 64, in wrapped_app
    raise exc
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\routing.py", line 72, in app
    response = await func(request)
               ^^^^^^^^^^^^^^^^^^^
  File "F:\corsor\test34\backend\venv\Lib\site-packages\fastapi\routing.py", line 296, in app
    content = await serialize_response(
              ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\corsor\test34\backend\venv\Lib\site-packages\fastapi\routing.py", line 155, in serialize_response
    raise ResponseValidationError(
fastapi.exceptions.ResponseValidationError: 2 validation errors:
  {'type': 'get_attribute_error', 'loc': ('response', 'owner'), 'msg': "Error extracting attribute: MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)", 'input': <app.models.project.Project object at 0x000002185D1F7410>, 'ctx': {'error': "MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)"}}
  {'type': 'get_attribute_error', 'loc': ('response', 'members'), 'msg': "Error extracting attribute: MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)", 'input': <app.models.project.Project object at 0x000002185D1F7410>, 'ctx': {'error': "MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)"}}

2025-06-21 17:53:07,338 - app.core.middleware - INFO - Request started: POST /api/v1/projects/
2025-06-21 17:53:07,339 - app.core.middleware - ERROR - Request failed: POST /api/v1/projects/ - Error: 2 validation errors:
  {'type': 'get_attribute_error', 'loc': ('response', 'owner'), 'msg': "Error extracting attribute: MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)", 'input': <app.models.project.Project object at 0x000002185CCB4490>, 'ctx': {'error': "MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)"}}
  {'type': 'get_attribute_error', 'loc': ('response', 'members'), 'msg': "Error extracting attribute: MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)", 'input': <app.models.project.Project object at 0x000002185CCB4490>, 'ctx': {'error': "MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)"}}
 - Duration: 0.0006s
Traceback (most recent call last):
  File "F:\corsor\test34\backend\venv\Lib\site-packages\anyio\streams\memory.py", line 97, in receive
    return self.receive_nowait()
           ^^^^^^^^^^^^^^^^^^^^^
  File "F:\corsor\test34\backend\venv\Lib\site-packages\anyio\streams\memory.py", line 92, in receive_nowait
    raise WouldBlock
anyio.WouldBlock

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\corsor\test34\backend\venv\Lib\site-packages\anyio\streams\memory.py", line 112, in receive
    raise EndOfStream
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\corsor\test34\backend\app\core\middleware.py", line 25, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\middleware\base.py", line 165, in call_next
    raise app_exc
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\middleware\base.py", line 151, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\middleware\cors.py", line 148, in simple_response
    await self.app(scope, receive, send)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\middleware\exceptions.py", line 65, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\_exception_handler.py", line 64, in wrapped_app
    raise exc
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\routing.py", line 756, in __call__
    await self.middleware_stack(scope, receive, send)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\routing.py", line 776, in app
    await route.handle(scope, receive, send)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\routing.py", line 297, in handle
    await self.app(scope, receive, send)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\routing.py", line 77, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\_exception_handler.py", line 64, in wrapped_app
    raise exc
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\routing.py", line 72, in app
    response = await func(request)
               ^^^^^^^^^^^^^^^^^^^
  File "F:\corsor\test34\backend\venv\Lib\site-packages\fastapi\routing.py", line 296, in app
    content = await serialize_response(
              ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\corsor\test34\backend\venv\Lib\site-packages\fastapi\routing.py", line 155, in serialize_response
    raise ResponseValidationError(
fastapi.exceptions.ResponseValidationError: 2 validation errors:
  {'type': 'get_attribute_error', 'loc': ('response', 'owner'), 'msg': "Error extracting attribute: MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)", 'input': <app.models.project.Project object at 0x000002185CCB4490>, 'ctx': {'error': "MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)"}}
  {'type': 'get_attribute_error', 'loc': ('response', 'members'), 'msg': "Error extracting attribute: MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)", 'input': <app.models.project.Project object at 0x000002185CCB4490>, 'ctx': {'error': "MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)"}}

2025-06-21 17:53:07,500 - app.core.middleware - INFO - Request started: POST /api/v1/projects/
2025-06-21 17:53:07,502 - app.core.middleware - ERROR - Request failed: POST /api/v1/projects/ - Error: 2 validation errors:
  {'type': 'get_attribute_error', 'loc': ('response', 'owner'), 'msg': "Error extracting attribute: MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)", 'input': <app.models.project.Project object at 0x000002185D509410>, 'ctx': {'error': "MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)"}}
  {'type': 'get_attribute_error', 'loc': ('response', 'members'), 'msg': "Error extracting attribute: MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)", 'input': <app.models.project.Project object at 0x000002185D509410>, 'ctx': {'error': "MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)"}}
 - Duration: 0.0020s
Traceback (most recent call last):
  File "F:\corsor\test34\backend\venv\Lib\site-packages\anyio\streams\memory.py", line 97, in receive
    return self.receive_nowait()
           ^^^^^^^^^^^^^^^^^^^^^
  File "F:\corsor\test34\backend\venv\Lib\site-packages\anyio\streams\memory.py", line 92, in receive_nowait
    raise WouldBlock
anyio.WouldBlock

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\corsor\test34\backend\venv\Lib\site-packages\anyio\streams\memory.py", line 112, in receive
    raise EndOfStream
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\corsor\test34\backend\app\core\middleware.py", line 25, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\middleware\base.py", line 165, in call_next
    raise app_exc
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\middleware\base.py", line 151, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\middleware\cors.py", line 148, in simple_response
    await self.app(scope, receive, send)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\middleware\exceptions.py", line 65, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\_exception_handler.py", line 64, in wrapped_app
    raise exc
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\routing.py", line 756, in __call__
    await self.middleware_stack(scope, receive, send)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\routing.py", line 776, in app
    await route.handle(scope, receive, send)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\routing.py", line 297, in handle
    await self.app(scope, receive, send)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\routing.py", line 77, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\_exception_handler.py", line 64, in wrapped_app
    raise exc
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
  File "F:\corsor\test34\backend\venv\Lib\site-packages\starlette\routing.py", line 72, in app
    response = await func(request)
               ^^^^^^^^^^^^^^^^^^^
  File "F:\corsor\test34\backend\venv\Lib\site-packages\fastapi\routing.py", line 296, in app
    content = await serialize_response(
              ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\corsor\test34\backend\venv\Lib\site-packages\fastapi\routing.py", line 155, in serialize_response
    raise ResponseValidationError(
fastapi.exceptions.ResponseValidationError: 2 validation errors:
  {'type': 'get_attribute_error', 'loc': ('response', 'owner'), 'msg': "Error extracting attribute: MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)", 'input': <app.models.project.Project object at 0x000002185D509410>, 'ctx': {'error': "MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)"}}
  {'type': 'get_attribute_error', 'loc': ('response', 'members'), 'msg': "Error extracting attribute: MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)", 'input': <app.models.project.Project object at 0x000002185D509410>, 'ctx': {'error': "MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)"}}

