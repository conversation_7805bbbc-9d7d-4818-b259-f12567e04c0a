#!/usr/bin/env python3
"""
简单的服务器启动脚本
"""
import sys
import os
import uvicorn

# 添加当前目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from app.main import app
    print("✓ FastAPI 应用加载成功")
    
    print("正在启动服务器...")
    print("服务器地址: http://127.0.0.1:8000")
    print("API 文档: http://127.0.0.1:8000/docs")
    print("按 Ctrl+C 停止服务器")
    
    uvicorn.run(
        app,
        host="127.0.0.1",
        port=8000,
        log_level="info",
        access_log=True
    )
    
except Exception as e:
    print(f"❌ 启动失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
