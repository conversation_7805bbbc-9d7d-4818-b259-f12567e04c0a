#!/usr/bin/env python3
"""
简单的服务器启动脚本 - 调试版本
"""
import sys
import os

# 添加当前目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

print("正在启动服务器...")
print(f"Python 版本: {sys.version}")
print(f"工作目录: {os.getcwd()}")

try:
    # 导入应用
    from app.main import app
    print("✓ FastAPI 应用加载成功")
    
    # 启动服务器
    import uvicorn
    print("✓ uvicorn 导入成功")
    
    print("启动服务器在 http://127.0.0.1:8000")
    print("按 Ctrl+C 停止服务器")
    
    # 使用更详细的日志
    uvicorn.run(
        "app.main:app",
        host="127.0.0.1",
        port=8000,
        log_level="debug",
        reload=False,
        access_log=True
    )
    
except KeyboardInterrupt:
    print("\n服务器已停止")
except Exception as e:
    print(f"❌ 启动失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
