import os
import sys
import uvicorn
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """
    启动应用程序
    """
    try:
        logger.info("正在启动应用...")
        
        # 将当前目录添加到 Python 路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        sys.path.insert(0, current_dir)
        
        # 启动 Uvicorn 服务器
        uvicorn.run(
            "app.main:app", 
            host="0.0.0.0",
            port=8000,
            reload=True,
            log_level="info"
        )
    except Exception as e:
        logger.error(f"启动失败: {str(e)}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    main() 