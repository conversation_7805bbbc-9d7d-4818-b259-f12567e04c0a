#!/usr/bin/env python3
"""
测试数据库连接
"""
import asyncio
import sys
import os

# 添加当前目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

async def test_db():
    try:
        print("测试数据库连接...")
        
        from app.db.session import SessionLocal
        from app.models.project import Project
        from sqlalchemy import select
        
        async with SessionLocal() as db:
            print("✓ 数据库会话创建成功")
            
            # 测试查询项目
            result = await db.execute(select(Project))
            projects = result.scalars().all()
            
            print(f"✓ 查询成功，找到 {len(projects)} 个项目")
            for project in projects:
                print(f"  - {project.name}")
                
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_db())
