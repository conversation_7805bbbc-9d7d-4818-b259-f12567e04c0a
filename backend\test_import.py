#!/usr/bin/env python3
"""
测试导入脚本
"""
import sys
import os

print("开始测试...")

# 添加当前目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    print("1. 测试基本导入...")
    import uvicorn
    print("✓ uvicorn 导入成功")
    
    print("2. 测试应用导入...")
    from app.main import app
    print("✓ FastAPI 应用导入成功")
    
    print("3. 测试数据库连接...")
    from app.db.session import engine
    print("✓ 数据库引擎创建成功")
    
    print("4. 测试配置...")
    from app.core.config import settings
    print(f"✓ 配置加载成功，数据库URL: {settings.DATABASE_URL}")
    
    print("所有测试通过！")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
