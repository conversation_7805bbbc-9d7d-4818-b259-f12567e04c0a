import asyncio
from app.db.session import SessionLocal
from app.models.project import Project
from sqlalchemy import select
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def verify_data():
    """Connects to the DB and prints projects."""
    logger.info("Attempting to connect to the database...")
    try:
        async with SessionLocal() as db:
            logger.info("Database session started.")
            
            result = await db.execute(select(Project))
            projects = result.scalars().all()
            
            if projects:
                logger.info(f"SUCCESS: Found {len(projects)} projects in the database.")
                for i, p in enumerate(projects):
                    print(f"  - Project {i+1}: ID={p.id}, Name='{p.name}', Description='{p.description}'")
            else:
                logger.error("FAILURE: The 'project' table is empty.")
                
    except Exception as e:
        logger.error(f"An error occurred while connecting to or querying the database: {e}", exc_info=True)

if __name__ == "__main__":
    asyncio.run(verify_data()) 