/* 清除默认样式，确保全屏布局 */
#root {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  text-align: left;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f0f2f5;
}

::-webkit-scrollbar-thumb {
  background: #bfbfbf;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #999;
}

/* 抽屉菜单自定义样式 */
.custom-drawer .ant-drawer-body {
  padding: 0;
}

/* 卡片悬停效果 */
.ant-card-hoverable:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
  transition: all 0.3s;
}

/* 页面内容区域 */
.dashboard-container {
  min-height: 100%;
}

/* 修复一些 Antd 组件的样式问题 */
.ant-layout-sider-children {
  display: flex;
  flex-direction: column;
}

.ant-menu-inline-collapsed .ant-menu-item {
  padding-inline: 0 !important;
  text-align: center;
}