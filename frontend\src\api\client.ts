import axios from 'axios';

const client = axios.create({
  baseURL: '/api/v1',
  headers: {
    'Content-Type': 'application/json',
  },
});

client.interceptors.request.use((config) => {
  const token = localStorage.getItem('access_token');
  // 只有当 token 存在且不是字符串 "true" 时才添加到请求头
  if (token && token !== 'true') {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
}, (error) => {
  return Promise.reject(error);
});

// 添加响应拦截器处理常见错误
client.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // 处理 401 Unauthorized 错误
    if (error.response && error.response.status === 401) {
      // 清除本地存储的认证信息
      localStorage.removeItem('access_token');
      localStorage.removeItem('user');
      localStorage.removeItem('username');
      
      // 如果不是登录页面，重定向到登录页
      if (window.location.pathname !== '/login') {
        window.location.href = '/login';
      }
    }
    
    return Promise.reject(error);
  }
);

export default client;
