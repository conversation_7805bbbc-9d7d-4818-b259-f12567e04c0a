// 应用配置
const config = {
  // API 基础 URL，优先使用环境变量，否则使用默认值
  apiBaseUrl: import.meta.env.VITE_API_BASE_URL || '/api/v1',
  
  // 应用标题
  appTitle: import.meta.env.VITE_APP_TITLE || '数据标注系统',
  
  // 认证配置
  auth: {
    tokenStorageKey: 'access_token',
    userStorageKey: 'user',
    usernameStorageKey: 'username',
  },
  
  // 默认分页配置
  pagination: {
    defaultPageSize: 10,
    pageSizeOptions: ['10', '20', '50', '100'],
  },
};

export default config; 