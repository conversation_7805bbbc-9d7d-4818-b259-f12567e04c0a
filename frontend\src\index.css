/* 基础设置 */
:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(0, 0, 0, 0.87);
  background-color: #f5f5f5;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 重置基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  margin: 0;
  display: flex;
  min-width: 320px;
  min-height: 100vh;
}

#root {
  width: 100%;
}

/* 链接样式 */
a {
  color: #1677ff;
  text-decoration: none;
  transition: color 0.3s;
}

a:hover {
  color: #4096ff;
}

/* 确保按钮和输入框等Ant Design组件显示正常 */
button, input, select, textarea {
  font-family: inherit;
}

/* 设置容器类组件的默认样式 */
.container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

/* 顶部菜单样式定制 */
.ant-menu-dark.ant-menu-horizontal {
  border-bottom: none;
}

.ant-menu-dark.ant-menu-horizontal > .ant-menu-item:hover,
.ant-menu-dark.ant-menu-horizontal > .ant-menu-item-selected {
  background-color: #1677ff;
}

/* 顶部搜索框样式 */
.ant-input-search .ant-input-group-addon {
  background-color: transparent;
}

.ant-input-search .ant-input {
  background-color: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.25);
  color: #fff;
}

.ant-input-search .ant-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

/* 抽屉菜单样式 */
.custom-drawer .ant-drawer-body {
  padding: 0;
}

/* 数据卡片悬停效果 */
.stat-card {
  transition: all 0.3s ease;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.stat-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

/* 美化滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #aaa;
}

/* 卡片标题样式 */
.ant-card-head {
  border-bottom: 1px solid #f0f0f0;
  padding: 0 16px;
}

.ant-card-head-title {
  font-weight: 600;
  font-size: 16px;
  padding: 12px 0;
}

/* 列表项美化 */
.ant-list-item {
  padding: 12px 0;
  transition: background-color 0.3s;
}

.ant-list-item:hover {
  background-color: #fafafa;
}

/* 标签样式美化 */
.ant-tag {
  border-radius: 4px;
  padding: 0 8px;
  margin-right: 8px;
  font-size: 12px;
}

/* 按钮样式美化 */
.ant-btn {
  border-radius: 4px;
  transition: all 0.3s;
}

.ant-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

/* 头像样式 */
.ant-avatar {
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 500;
}

/* 进度条样式 */
.ant-progress-bg {
  transition: all 0.5s cubic-bezier(0.08, 0.82, 0.17, 1);
}

/* 标签页样式 */
.ant-tabs-tab {
  transition: all 0.3s;
  padding: 8px 16px;
}

.ant-tabs-tab:hover {
  color: #1677ff;
}

.ant-tabs-tab-active {
  font-weight: 500;
}

.ant-tabs-ink-bar {
  height: 3px;
  border-radius: 3px 3px 0 0;
}

/* 面包屑样式 */
.ant-breadcrumb {
  font-size: 13px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }
  
  /* 调整移动端的顶部菜单 */
  .top-menu-mobile {
    width: 100%;
    overflow-x: auto;
    white-space: nowrap;
    -webkit-overflow-scrolling: touch;
  }
  
  /* 移动端隐藏非必要元素 */
  .hide-on-mobile {
    display: none;
  }
  
  /* 移动端卡片内边距调整 */
  .ant-card-body {
    padding: 16px;
  }
}

/* 防止图片溢出容器边界 */
img {
  max-width: 100%;
  height: auto;
}

/* 页面标题区域样式 */
.page-title-section {
  margin-bottom: 24px;
}

.page-title-section h2 {
  margin-bottom: 8px;
}

/* 卡片内容区域样式 */
.ant-card-body {
  padding: 20px;
}

/* 统计数字样式 */
.ant-statistic-content {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 自定义阴影效果 */
.shadow-sm {
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.shadow-md {
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.shadow-lg {
  box-shadow: 0 10px 15px rgba(0,0,0,0.1);
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* 布局容器样式 */
.ant-layout {
  background: #f5f7fa;
}

/* 侧边栏样式优化 */
.ant-layout-sider {
  box-shadow: 2px 0 8px rgba(0,0,0,0.15);
}

/* 顶部导航栏样式优化 */
.top-nav-bar {
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

/* 内容区域样式 */
.ant-layout-content {
  padding: 24px;
}

/* 页脚样式 */
.ant-layout-footer {
  padding: 16px 50px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
  background: #f5f7fa;
}

/* 表格样式优化 */
.ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 500;
}

/* 表单样式优化 */
.ant-form-item-label > label {
  font-weight: 500;
}

/* 下拉菜单样式 */
.ant-dropdown-menu {
  border-radius: 4px;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
}

/* 添加卡片悬停效果 */
.hover-card {
  transition: all 0.3s ease;
  border-radius: 8px;
}

.hover-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

/* 菜单悬停效果 */
.ant-menu-item {
  transition: all 0.3s ease;
}

.ant-menu-item:hover {
  background-color: rgba(24, 144, 255, 0.1);
}

/* 统一圆角样式 */
.ant-card {
  border-radius: 8px;
  overflow: hidden;
}

.ant-btn {
  transition: all 0.3s;
}

.ant-btn:hover {
  transform: translateY(-1px);
}

/* 适配移动设备的响应式样式 */
@media (max-width: 768px) {
  .ant-card-body {
    padding: 16px;
  }
  
  .ant-card-head-title {
    padding: 12px 0;
  }
}

/* 增强导航栏视觉效果 */
.ant-menu-horizontal {
  border-bottom: none;
}

.ant-menu-horizontal > .ant-menu-item::after,
.ant-menu-horizontal > .ant-menu-submenu::after {
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  content: "";
  position: absolute;
  left: 20%;
  right: 20%;
  bottom: 0;
  height: 2px;
  background-color: #1890ff;
  opacity: 0;
}

.ant-menu-horizontal > .ant-menu-item-active::after,
.ant-menu-horizontal > .ant-menu-item-open::after,
.ant-menu-horizontal > .ant-menu-item-selected::after,
.ant-menu-horizontal > .ant-menu-submenu-active::after,
.ant-menu-horizontal > .ant-menu-submenu-open::after,
.ant-menu-horizontal > .ant-menu-submenu-selected::after {
  left: 0;
  right: 0;
  opacity: 1;
}

/* 首页样式 */
.home-page {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

/* 顶部导航栏固定样式 */
.ant-layout-header.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 侧边栏固定样式 */
.ant-layout-sider.fixed-sider {
  position: fixed;
  left: 0;
  height: calc(100vh - 64px);
  overflow: auto;
  z-index: 900;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);
}

/* 内容区域样式 */
.site-layout-content {
  min-height: calc(100vh - 64px - 48px);
  padding: 24px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* 面包屑导航样式 */
.breadcrumb-container {
  margin-bottom: 16px;
  padding: 8px 0;
}

/* 页面标题样式 */
.page-title {
  margin-bottom: 24px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 16px;
}

/* 页脚样式 */
.ant-layout-footer {
  text-align: center;
  padding: 12px 50px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
  background-color: #f5f5f5;
}

/* 菜单项过渡效果 */
.ant-menu-item, .ant-menu-submenu-title {
  transition: all 0.3s ease;
}

/* 用户下拉菜单样式 */
.user-dropdown {
  cursor: pointer;
  padding: 0 12px;
  display: flex;
  align-items: center;
  transition: all 0.3s;
  border-radius: 4px;
}

.user-dropdown:hover {
  background-color: rgba(0, 0, 0, 0.025);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .home-page {
    padding: 16px;
  }
  
  .site-layout-content {
    padding: 16px;
  }
  
  .ant-layout-header {
    padding: 0 16px;
  }
  
  /* 移动端顶部导航栏样式调整 */
  .ant-menu-horizontal {
    white-space: nowrap;
    overflow-x: auto;
    overflow-y: hidden;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none; /* Firefox */
  }
  
  .ant-menu-horizontal::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Edge */
  }
  
  /* 移动端系统名称样式调整 */
  .ant-typography.ant-typography-h4 {
    font-size: 16px;
  }
}

/* 首页特殊样式 */
.home-page .ant-card {
  margin-bottom: 24px;
}

.home-page .ant-statistic {
  text-align: center;
  padding: 16px 0;
}

.home-page .ant-card-head {
  border-bottom-color: #f0f0f0;
}

.home-page .ant-card-head-title {
  font-weight: 600;
  font-size: 16px;
}

/* 首页功能模块卡片样式 */
.function-card {
  height: 100%;
  text-align: center;
  transition: all 0.3s;
}

.function-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

/* 首页统计数据样式 */
.ant-statistic-title {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
}

.ant-statistic-content {
  font-size: 24px;
  font-weight: 600;
}

/* 系统标题样式 */
.ant-typography.ant-typography-h4 {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}