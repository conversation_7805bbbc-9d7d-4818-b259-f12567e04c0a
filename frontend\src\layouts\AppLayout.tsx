import React, { useState, useEffect } from 'react';
import { Outlet, useNavigate, useLocation, Link } from 'react-router-dom';
import {
  DashboardOutlined,
  ShoppingCartOutlined,
  AppstoreOutlined,
  FileTextOutlined,
  UserOutlined,
  TeamOutlined,
  SettingOutlined,
  ShopOutlined,
  LogoutOutlined,
  HomeOutlined,
  BarsOutlined,
  AreaChartOutlined,
  Bar<PERSON>hartOutlined,
  Pie<PERSON>hartOutlined,
  ProfileOutlined,
  LineChartOutlined,
  BankOutlined,
  GlobalOutlined,
  SafetyCertificateOutlined,
  CustomerServiceOutlined,
  BellOutlined,
  MessageOutlined,
  QuestionCircleOutlined,
  SearchOutlined,
  MailOutlined,
  CalendarOutlined,
  FileSearchOutlined,
  DownOutlined,
  DollarOutlined,
  ContainerOutlined,
  DatabaseOutlined,
  FolderOutlined,
  TagOutlined,
  FileOutlined,
  ShareAltOutlined,
  NotificationOutlined,
  FieldTimeOutlined,
  ScheduleOutlined,
  CheckCircleOutlined,
  StarOutlined,
  <PERSON>lusterOutlined,
  SolutionOutlined,
  ProjectOutlined,
  CheckSquareOutlined,
  ToolOutlined,
  Clock<PERSON>ircleOutlined,
  CommentOutlined,
  UnorderedListOutlined,
  InboxOutlined,
  PlusOutlined,
  FormOutlined,
  FundViewOutlined,
  ReadOutlined,
  BookOutlined,
  FileImageOutlined,
  BranchesOutlined,
  ImportOutlined,
  UploadOutlined,
  CloudUploadOutlined,
  ApiOutlined,
  ExperimentOutlined,
  ClearOutlined,
  NodeExpandOutlined,
  ScissorOutlined,
  ExportOutlined,
  HistoryOutlined,
  SwapOutlined,
  AudioOutlined,
  VideoCameraOutlined,
  UserSwitchOutlined,
  TagsOutlined,
  ControlOutlined,
  SafetyOutlined,
  AuditOutlined,
  RobotOutlined,
  BugOutlined,
  PlayCircleOutlined
} from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { Breadcrumb, Layout, Menu, theme, Button, Typography, Avatar, Drawer, Badge, Input, Dropdown, Space, Tooltip } from 'antd';
import useAuthStore from '../store/authStore';
import logoImage from '../assets/logo.svg'; // 导入自定义logo图片

const { Header, Content, Footer, Sider } = Layout;
const { Title, Text } = Typography;

type MenuItem = Required<MenuProps>['items'][number];

function getItem(
  label: React.ReactNode,
  key: React.Key,
  icon?: React.ReactNode,
  children?: MenuItem[],
): MenuItem {
  return {
    key,
    icon,
    children,
    label,
  } as MenuItem;
}

// 顶部菜单项 - 数据标注系统
const topMenuItems: MenuItem[] = [
  getItem('首页', '/home', <HomeOutlined />),
  getItem('项目管理', '/projects', <ProjectOutlined />),
  getItem('数据集', '/datasets', <DatabaseOutlined />),
  getItem('标注工作台', '/workspace', <CheckSquareOutlined />),
  getItem('测试', '/testing', <ExperimentOutlined />),
  getItem('系统管理', '/system', <SettingOutlined />),
];

// 1. 首页模块
const homeMenuItems: MenuItem[] = [
  getItem('数据概览', 'home-dashboard', <DashboardOutlined />, [
    getItem('标注进度', 'progress-stats', <AreaChartOutlined />),
    getItem('质量指标', 'quality-metrics', <PieChartOutlined />),
    getItem('个人统计', 'personal-stats', <BarChartOutlined />),
  ]),
  getItem('我的任务', 'my-tasks', <ProfileOutlined />, [
    getItem('待处理', 'pending-tasks', <ClockCircleOutlined />),
    getItem('已完成', 'completed-tasks', <CheckCircleOutlined />),
    getItem('质检任务', 'qa-tasks', <SafetyCertificateOutlined />),
  ]),
  getItem('消息通知', 'notifications', <BellOutlined />, [
    getItem('系统通知', 'system-notifications', <NotificationOutlined />),
    getItem('任务通知', 'task-notifications', <SolutionOutlined />),
    getItem('质检反馈', 'qa-feedback', <CommentOutlined />),
  ]),
];

// 2. 项目管理模块
const projectMenuItems: MenuItem[] = [
  getItem('项目列表', 'project-list', <UnorderedListOutlined />, [
    getItem('全部项目', 'all-projects', <AppstoreOutlined />),
    getItem('我的项目', 'my-projects', <UserOutlined />),
    getItem('已归档', 'archived-projects', <InboxOutlined />),
  ]),
  getItem('项目创建', 'project-create', <PlusOutlined />),
  getItem('项目监控', 'project-monitoring', <FundViewOutlined />, [
     getItem('进度监控', 'progress-monitoring', <LineChartOutlined />),
     getItem('质量监控', 'quality-monitoring', <PieChartOutlined />),
     getItem('成员表现', 'member-performance', <TeamOutlined />),
  ]),
  getItem('知识库', 'knowledge-base', <ReadOutlined />, [
    getItem('标注指南', 'annotation-guides', <BookOutlined />),
    getItem('常见问题', 'faqs', <QuestionCircleOutlined />),
    getItem('标准示例', 'standard-examples', <FileImageOutlined />),
  ]),
];

// 3. 数据集模块
const datasetMenuItems: MenuItem[] = [
  getItem('数据集管理', 'dataset-management', <DatabaseOutlined />, [
    getItem('数据集列表', 'dataset-list', <UnorderedListOutlined />),
    getItem('数据集版本', 'dataset-versions', <BranchesOutlined />),
  ]),
  getItem('数据操作', 'data-operations', <ToolOutlined />, [
    getItem('数据导入', 'import-data', <ImportOutlined />),
    getItem('数据预处理', 'preprocess-data', <ExperimentOutlined />),
    getItem('数据导出', 'export-data', <ExportOutlined />),
  ]),
];

// 4. 标注工作台模块
const workspaceMenuItems: MenuItem[] = [
  getItem('标注任务', 'annotation-tasks', <CheckSquareOutlined />, [
    getItem('待标注', 'pending-annotation', <ClockCircleOutlined />),
    getItem('标注历史', 'annotation-history', <HistoryOutlined />),
  ]),
  getItem('标注工具', 'annotation-tools', <ToolOutlined />, [
    getItem('图像标注', 'image-annotation', <FileImageOutlined />),
    getItem('文本标注', 'text-annotation', <FileTextOutlined />),
    getItem('音频标注', 'audio-annotation', <AudioOutlined />),
    getItem('视频标注', 'video-annotation', <VideoCameraOutlined />),
  ]),
  getItem('质检任务', 'qa-tasks-workspace', <SafetyCertificateOutlined />, [
    getItem('待质检', 'pending-qa', <ClockCircleOutlined />),
    getItem('质检历史', 'qa-history', <HistoryOutlined />),
  ]),
  getItem('协作空间', 'collaboration', <TeamOutlined />, [
    getItem('疑难样本', 'difficult-samples', <QuestionCircleOutlined />),
    getItem('规则讨论', 'rule-discussions', <CommentOutlined />),
  ]),
];

// 5. 系统管理模块
const systemMenuItems: MenuItem[] = [
  getItem('用户管理', 'user-management', <UserOutlined />, [
    getItem('用户列表', 'user-list', <TeamOutlined />),
    getItem('角色权限', 'roles-permissions', <SafetyCertificateOutlined />),
    getItem('团队管理', 'team-management', <ClusterOutlined />),
  ]),
  getItem('标注配置', 'annotation-config', <SettingOutlined />, [
    getItem('标签体系', 'label-management', <TagsOutlined />),
    getItem('标注模板', 'template-management', <FormOutlined />),
    getItem('快捷键设置', 'shortcut-settings', <ControlOutlined />),
  ]),
  getItem('质量控制', 'quality-control', <SafetyOutlined />, [
    getItem('质检规则', 'qa-rules', <AuditOutlined />),
    getItem('一致性检查', 'consistency-check', <CheckCircleOutlined />),
    getItem('自动质检', 'auto-qa-config', <RobotOutlined />),
  ]),
  getItem('系统日志', 'system-logs', <FileTextOutlined />),
];

// 新增: 测试模块菜单
const testingMenuItems: MenuItem[] = [
  getItem('测试仪表板', 'testing-dashboard', <DashboardOutlined />, [
    getItem('测试概览', 'test-overview', <PieChartOutlined />),
    getItem('测试进度', 'test-progress', <LineChartOutlined />),
    getItem('质量报告', 'test-quality', <BarChartOutlined />),
  ]),
  getItem('测试用例', 'test-cases', <FileTextOutlined />, [
    getItem('用例库', 'test-case-library', <UnorderedListOutlined />),
    getItem('创建用例', 'create-test-case', <FormOutlined />),
    getItem('用例执行', 'execute-test-case', <PlayCircleOutlined />),
  ]),
  getItem('缺陷管理', 'defect-management', <BugOutlined />, [
    getItem('缺陷列表', 'defect-list', <UnorderedListOutlined />),
    getItem('缺陷统计', 'defect-statistics', <PieChartOutlined />),
    getItem('缺陷分析', 'defect-analysis', <AreaChartOutlined />),
  ]),
  getItem('测试配置', 'test-settings', <SettingOutlined />, [
    getItem('环境配置', 'environment-settings', <ToolOutlined />),
    getItem('测试计划', 'test-plans', <ScheduleOutlined />),
  ]),
];

// 面包屑路径映射 - 数据标注系统
const breadcrumbMap: Record<string, string[]> = {
  // 顶级模块
  'home': ['首页'],
  'projects': ['项目管理'],
  'datasets': ['数据集'],
  'workspace': ['标注工作台'],
  'testing': ['测试'],
  'system': ['系统管理'],

  // 首页
  'progress-stats': ['首页', '数据概览', '标注进度'],
  'quality-metrics': ['首页', '数据概览', '质量指标'],
  'personal-stats': ['首页', '数据概览', '个人统计'],
  'pending-tasks': ['首页', '我的任务', '待处理'],
  'completed-tasks': ['首页', '我的任务', '已完成'],
  'qa-tasks': ['首页', '我的任务', '质检任务'],
  'system-notifications': ['首页', '消息通知', '系统通知'],
  'task-notifications': ['首页', '消息通知', '任务通知'],
  'qa-feedback': ['首页', '消息通知', '质检反馈'],

  // 项目管理
  'all-projects': ['项目管理', '项目列表', '全部项目'],
  'my-projects': ['项目管理', '项目列表', '我的项目'],
  'archived-projects': ['项目管理', '项目列表', '已归档'],
  'project-create': ['项目管理', '项目创建'],
  'progress-monitoring': ['项目管理', '项目监控', '进度监控'],
  'quality-monitoring': ['项目管理', '项目监控', '质量监控'],
  'member-performance': ['项目管理', '项目监控', '成员表现'],
  'annotation-guides': ['项目管理', '知识库', '标注指南'],
  'faqs': ['项目管理', '知识库', '常见问题'],
  'standard-examples': ['项目管理', '知识库', '标准示例'],

  // 数据集
  'dataset-list': ['数据集', '数据集管理', '数据集列表'],
  'dataset-versions': ['数据集', '数据集管理', '数据集版本'],
  'import-data': ['数据集', '数据操作', '数据导入'],
  'preprocess-data': ['数据集', '数据操作', '数据预处理'],
  'export-data': ['数据集', '数据操作', '数据导出'],

  // 标注工作台
  'pending-annotation': ['标注工作台', '标注任务', '待标注'],
  'annotation-history': ['标注工作台', '标注任务', '标注历史'],
  'image-annotation': ['标注工作台', '标注工具', '图像标注'],
  'text-annotation': ['标注工作台', '标注工具', '文本标注'],
  'audio-annotation': ['标注工作台', '标注工具', '音频标注'],
  'video-annotation': ['标注工作台', '标注工具', '视频标注'],
  'pending-qa': ['标注工作台', '质检任务', '待质检'],
  'qa-history': ['标注工作台', '质检任务', '质检历史'],
  'difficult-samples': ['标注工作台', '协作空间', '疑难样本'],
  'rule-discussions': ['标注工作台', '协作空间', '规则讨论'],

  // 系统管理
  'user-list': ['系统管理', '用户管理', '用户列表'],
  'roles-permissions': ['系统管理', '用户管理', '角色权限'],
  'team-management': ['系统管理', '用户管理', '团队管理'],
  'label-management': ['系统管理', '标注配置', '标签体系'],
  'template-management': ['系统管理', '标注配置', '标注模板'],
  'shortcut-settings': ['系统管理', '标注配置', '快捷键设置'],
  'qa-rules': ['系统管理', '质量控制', '质检规则'],
  'consistency-check': ['系统管理', '质量控制', '一致性检查'],
  'auto-qa-config': ['系统管理', '质量控制', '自动质检'],
  'system-logs': ['系统管理', '系统日志'],

  // 测试模块
  'test-overview': ['测试', '测试仪表板', '测试概览'],
  'test-progress': ['测试', '测试仪表板', '测试进度'],
  'test-quality': ['测试', '测试仪表板', '质量报告'],
  'test-case-library': ['测试', '测试用例', '用例库'],
  'create-test-case': ['测试', '测试用例', '创建用例'],
  'execute-test-case': ['测试', '测试用例', '用例执行'],
  'defect-list': ['测试', '缺陷管理', '缺陷列表'],
  'defect-statistics': ['测试', '缺陷管理', '缺陷统计'],
  'defect-analysis': ['测试', '缺陷管理', '缺陷分析'],
  'environment-settings': ['测试', '测试配置', '环境配置'],
  'test-plans': ['测试', '测试配置', '测试计划'],
};

// 模拟通知数据
const notifications = [
  { id: 1, title: '您的报销申请已通过审批', time: '昨天', read: true },
  { id: 2, title: '您的报销申请已通过审批', time: '昨天', read: true },
  { id: 4, title: '您的报销申请已通过审批', time: '昨天', read: true }
];

// 模拟消息数据
const messages = [
  { id: 3, sender: '系统通知', content: '您的账号权限已更新', time: '前天', read: true }
];

// 为每个顶部菜单定义对应的侧边栏菜单内容
const getMenuItemsByTopMenu = (topMenuItem: string): MenuItem[] => {
  switch (topMenuItem) {
    case '/home':
      return homeMenuItems;
    case '/projects':
      return projectMenuItems;
    case '/datasets':
      return datasetMenuItems;
    case '/workspace':
      return workspaceMenuItems;
    case '/testing':
      return testingMenuItems;
    case '/system':
      return systemMenuItems;
    default:
      return [];
  }
};

const AppLayout: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [mobile, setMobile] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [openKeys, setOpenKeys] = useState<string[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [searchValue, setSearchValue] = useState('');
  const [showSidebar, setShowSidebar] = useState(true);
  const [currentSidebarMenuItems, setCurrentSidebarMenuItems] = useState<MenuItem[]>([]);
  const [selectedTopMenu, setSelectedTopMenu] = useState('');
  const [currentBreadcrumb, setCurrentBreadcrumb] = useState<string[]>([]);
  
  const {
    token: { colorBgContainer, borderRadiusLG, colorPrimary },
  } = theme.useToken();
  
  const navigate = useNavigate();
  const location = useLocation();
  const { logout } = useAuthStore();
  
  useEffect(() => {
    const checkMobile = () => {
      const isMobile = window.innerWidth < 992;
      setMobile(isMobile);
      if (isMobile) {
        setCollapsed(true);
      }
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  useEffect(() => {
    const path = location.pathname.replace(/^\//, '');
    const pathSegments = path.split('/');
    const topLevelPath = `/${pathSegments[0]}`;

    // 1. 更新顶部和侧边栏菜单
    setSelectedTopMenu(topLevelPath);
    const newSidebarMenuItems = getMenuItemsByTopMenu(topLevelPath);
    setCurrentSidebarMenuItems(newSidebarMenuItems);
    setShowSidebar(topLevelPath !== '/home');
    
    // 2. 更新面包屑
    const breadcrumbKey = pathSegments.length > 1 ? pathSegments.slice(1).join('/') : pathSegments[0] || 'home';
    setCurrentBreadcrumb(breadcrumbMap[breadcrumbKey] || []);
    
    // 3. 更新选中的菜单项和展开的父菜单
    if (pathSegments.length > 1) {
        const selectedKey = pathSegments.slice(1).join('/');
        setSelectedKeys([selectedKey]);

        // 修正：重写 findParentKeys 函数，确保能正确查找所有层级的父节点
        const findParentKeys = (items: MenuItem[], key: string): string[] => {
          const result: string[] = [];
          const find = (currentItems: MenuItem[], targetKey: string, currentPath: string[]): boolean => {
            for (const item of currentItems) {
              if (!item) continue;
              const itemKey = item.key as string;
              if (item.key === targetKey) {
                result.push(...currentPath);
                return true;
              }
              if (item && 'children' in item && item.children) {
                if (find(item.children as MenuItem[], targetKey, [...currentPath, itemKey])) {
                  // 如果在子节点中找到了，就不要再继续查找兄弟节点了
                  // 并且将当前路径添加到结果中
                  result.push(...currentPath);
                  return true;
                }
              }
            }
            return false;
          }
          
          // 为了找到所有父级，我们需要一个稍微不同的策略
          // 我们需要遍历并构建路径，如果找到，返回路径
          const findPath = (items: MenuItem[], key: string): string[] | null => {
            for(const item of items) {
                if(!item) continue;
                if(item.key === key) return [item.key as string];

                if(item && 'children' in item && item.children) {
                    const path = findPath(item.children, key);
                    if(path) {
                        return [item.key as string, ...path];
                    }
                }
            }
            return null;
          }
          
          const path = findPath(items, key);
          return path ? path.slice(0, -1) : []; // 返回除自身外的所有父级key
        };
        
        const parentKeys = findParentKeys(newSidebarMenuItems, selectedKey);
        // 修正：使用函数式更新，合并而不是替换 openKeys，以保留用户手动打开的菜单
        if (parentKeys.length > 0) {
          setOpenKeys(prevOpenKeys => [...new Set([...prevOpenKeys, ...parentKeys])]);
        }
    } else {
        setSelectedKeys([]);
        // 当返回顶级模块时，清空所有展开的键
        setOpenKeys([]); 
    }
  }, [location.pathname]);

  // 处理顶部菜单点击
  const handleTopMenuClick = ({ key }: { key: string }) => {
    setOpenKeys([]); // 切换主模块时，清空展开的子菜单
    const defaultRoutes: Record<string, string> = {
      '/home': '/home/<USER>',
      '/projects': '/projects/all-projects',
      '/datasets': '/datasets/dataset-list',
      '/workspace': '/workspace/pending-annotation',
      '/system': '/system/user-list',
    };
    navigate(defaultRoutes[key] || key);
    if (mobile) setDrawerVisible(false);
  };
  
  // 处理侧边栏菜单点击
  const handleSideMenuClick = ({ key }: { key:string }) => {
    const topLevelPath = selectedTopMenu.replace(/^\//, '');
    if (topLevelPath) navigate(`/${topLevelPath}/${key}`);
    if (mobile) setDrawerVisible(false);
  };
  
  const handleLogout = () => {
    logout();
    navigate('/login');
  };
  
  // 处理子菜单展开/折叠
  const handleOpenChange = (keys: string[]) => {
    setOpenKeys(keys);
  };
  
  const handleSearch = () => {
    if (searchValue) console.log('搜索：', searchValue);
  };

  const sideMenuContent = (
    <Menu 
      theme="dark" 
      mode="inline"
      selectedKeys={selectedKeys}
      openKeys={openKeys}
      onOpenChange={handleOpenChange}
      items={currentSidebarMenuItems} 
      onClick={handleSideMenuClick}
      style={{ borderRight: 0, height: '100%', overflowY: 'auto' }}
    />
  );

  const notificationMenu = (
    <div style={{ width: 320, background: colorBgContainer, borderRadius: borderRadiusLG, boxShadow: '0 6px 16px rgba(0,0,0,0.15)' }}>
      <div style={{ padding: '12px 16px', borderBottom: '1px solid #f0f0f0' }}>
        <Text strong>通知</Text>
      </div>
      <Menu items={notifications.map(n => ({ key: n.id, label: n.title }))} />
    </div>
  );

  const messageMenu = (
    <div style={{ width: 320, background: colorBgContainer, borderRadius: borderRadiusLG, boxShadow: '0 6px 16px rgba(0,0,0,0.15)' }}>
       <div style={{ padding: '12px 16px', borderBottom: '1px solid #f0f0f0' }}>
        <Text strong>消息</Text>
      </div>
       <Menu items={messages.map(m => ({ key: m.id, label: `${m.sender}: ${m.content}`}))} />
    </div>
  );

  const userMenu = (
    <Menu
      onClick={({ key }) => {
        if (key === 'logout') handleLogout();
        // else navigate(`/profile/${key}`);
      }}
      items={[
        { key: 'personal-center', label: '个人中心', icon: <UserOutlined /> },
        { key: 'account-settings', label: '账号设置', icon: <SettingOutlined /> },
        { type: 'divider' },
        { key: 'logout', danger: true, label: '退出登录', icon: <LogoutOutlined /> },
      ]}
    />
  );
  
  const TopMenuBar = () => (
    <Header style={{ 
      background: '#fff', padding: '0 24px', display: 'flex', alignItems: 'center', justifyContent: 'space-between',
      borderBottom: '1px solid #f0f0f0', position: 'fixed', top: 0, width: '100%', zIndex: 101
    }}>
      <div style={{ display: 'flex', alignItems: 'center', overflow: 'hidden' }}>
        <div style={{ display: 'flex', alignItems: 'center', marginRight: '16px', flexShrink: 0 }}>
           <img 
             src={logoImage} 
             alt="logo" 
             style={{ 
               height: 32, 
               marginRight: 8,
               // 移除滤镜，因为新logo已经有正确的颜色
               transform: 'scale(0.9)'
             }} 
           />
           <Title level={4} style={{ margin: 0, color: colorPrimary, whiteSpace: 'nowrap' }}>数据标注系统</Title>
        </div>
        <div className={mobile ? "top-menu-mobile" : ""} style={{ overflow: 'hidden' }}>
          <Menu
            mode="horizontal" selectedKeys={[selectedTopMenu]} onClick={handleTopMenuClick} items={topMenuItems}
            style={{ backgroundColor: 'transparent', borderBottom: 'none', lineHeight: '62px' }} theme="light"
          />
        </div>
      </div>
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <Space size={mobile ? 8 : 20} wrap={false}>
           {mobile && showSidebar && <Button type="text" icon={<BarsOutlined />} onClick={() => setDrawerVisible(true)} />}
          {!mobile && (
            <Tooltip title="全局搜索">
              <Input
                placeholder="搜索..." prefix={<SearchOutlined />} style={{ width: 200 }}
                value={searchValue} onChange={e => setSearchValue(e.target.value)} onPressEnter={handleSearch} allowClear
              />
            </Tooltip>
          )}
          <Tooltip title="通知中心">
            <Dropdown overlay={notificationMenu} trigger={['click']} placement="bottomRight" arrow={{ pointAtCenter: true }}>
              <Badge count={notifications.length} size="small">
                <Button type="text" shape="circle" icon={<BellOutlined />} style={{ fontSize: mobile ? '16px' : '18px' }} />
              </Badge>
            </Dropdown>
          </Tooltip>
          {!mobile && (
            <>
              <Tooltip title="消息中心">
                <Dropdown overlay={messageMenu} trigger={['click']} placement="bottomRight" arrow={{ pointAtCenter: true }}>
                  <Badge count={messages.length} size="small">
                    <Button type="text" shape="circle" icon={<MailOutlined />} style={{ fontSize: '18px' }} />
                  </Badge>
                </Dropdown>
              </Tooltip>
              <Tooltip title="帮助文档">
                <Button type="text" shape="circle" icon={<QuestionCircleOutlined />} style={{ fontSize: '18px' }} />
              </Tooltip>
            </>
          )}
          <Dropdown overlay={userMenu} trigger={['click']} placement="bottomRight" arrow={{ pointAtCenter: true }}>
            <div style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
              <Avatar style={{ backgroundColor: colorPrimary }}>{localStorage.getItem('username')?.charAt(0)?.toUpperCase() || 'A'}</Avatar>
              {!mobile && (
                <>
                  <Text style={{ marginLeft: 8 }}>{localStorage.getItem('username') || 'Admin'}</Text>
                  <DownOutlined style={{ marginLeft: 8, fontSize: 12, color: '#666' }}/>
                </>
              )}
            </div>
          </Dropdown>
        </Space>
      </div>
    </Header>
  );

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <TopMenuBar />
      <Layout style={{ marginTop: 64 }}>
        {showSidebar && !mobile && (
          <Sider
            width={220} theme="dark" collapsible collapsed={collapsed} onCollapse={setCollapsed}
            style={{ background: '#001529', position: 'fixed', left: 0, top: 64, bottom: 0, zIndex: 100, overflow: 'auto' }}
          >
            {sideMenuContent}
          </Sider>
        )}
        {showSidebar && mobile && (
           <Drawer
             placement="left" closable={false} onClose={() => setDrawerVisible(false)} open={drawerVisible}
             width={220} bodyStyle={{ padding: 0, background: '#001529' }}
           >
             {sideMenuContent}
           </Drawer>
        )}
        <Layout style={{ 
          padding: '0 24px 24px', marginLeft: showSidebar ? (mobile ? 0 : (collapsed ? 80 : 220)) : 0, transition: 'margin-left 0.2s'
        }}>
          {location.pathname !== '/home' && (
            <Breadcrumb style={{ margin: '16px 0' }}>
              <Breadcrumb.Item><Link to="/home">首页</Link></Breadcrumb.Item>
              {currentBreadcrumb.map((item, index) => <Breadcrumb.Item key={index}>{item}</Breadcrumb.Item>)}
            </Breadcrumb>
          )}
          <Content style={{
             background: colorBgContainer, padding: 24, margin: 0, minHeight: 280, borderRadius: borderRadiusLG,
          }}>
            <Outlet />
          </Content>
          <Footer style={{ textAlign: 'center' }}>
            数据标注系统 ©{new Date().getFullYear()} 版权所有
          </Footer>
        </Layout>
      </Layout>
    </Layout>
  );
};

export default AppLayout;