import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.tsx'
// Ant Design 5 使用 antd/dist/antd.css 而不是 reset.css
import 'antd/dist/reset.css'; 
import './index.css'
import { ConfigProvider } from 'antd'
import zhCN from 'antd/locale/zh_CN';

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <ConfigProvider
      locale={zhCN}
      theme={{
        token: {
          colorPrimary: '#1677ff',
          borderRadius: 6,
        },
      }}
    >
      <App />
    </ConfigProvider>
  </React.StrictMode>,
)