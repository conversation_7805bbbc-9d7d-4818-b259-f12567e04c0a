import React from 'react';
import { Result, Button } from 'antd';
import { useNavigate } from 'react-router-dom';

interface EmptyPageProps {
  title?: string;
}

const EmptyPage: React.FC<EmptyPageProps> = ({ title = "页面开发中" }) => {
  const navigate = useNavigate();
  
  return (
    <Result
      status="info"
      title={title}
      subTitle="该功能模块正在积极开发中，敬请期待！"
      extra={
        <Button type="primary" onClick={() => navigate('/dashboard')}>
          返回工作台
        </Button>
      }
    />
  );
};

export default EmptyPage;