.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #1677ff 0%, #06172f 100%);
  padding: 20px;
}

.login-wrapper {
  display: flex;
  flex-direction: column;
  max-width: 420px;
  width: 100%;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.login-logo {
  padding: 30px 0;
  text-align: center;
  background: linear-gradient(to right, #f0f5ff, #e6f7ff);
}

.logo-container {
  display: flex;
  justify-content: center;
  margin-bottom: 15px;
}

.logo-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  background-color: #1677ff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: bold;
}

.system-title {
  margin: 0;
  color: #333;
}

.login-content {
  padding: 30px;
}

.login-form {
  width: 100%;
}

.login-button {
  width: 100%;
  height: 44px;
  border-radius: 6px;
  font-size: 16px;
  margin-top: 10px;
  box-shadow: 0 2px 4px rgba(22, 119, 255, 0.2);
}

.login-footer {
  text-align: center;
  padding-top: 10px;
}

/* 响应式设计 */
@media (max-width: 576px) {
  .login-wrapper {
    max-width: 100%;
  }
  
  .login-content {
    padding: 20px;
  }
  
  .logo-icon {
    width: 50px;
    height: 50px;
    font-size: 18px;
  }
}