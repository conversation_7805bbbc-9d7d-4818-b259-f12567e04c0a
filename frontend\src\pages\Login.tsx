import React, { useEffect, useState } from 'react';
import { Button, Form, Input, message, Typography, Divider, Alert } from 'antd';
import { UserOutlined, LockOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import useAuthStore from '../store/authStore';
import './Login.css';

const { Title, Text } = Typography;

const Login: React.FC = () => {
  const navigate = useNavigate();
  const { login, isAuthenticated } = useAuthStore();
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const onFinish = async (values: any) => {
    setError(null);
    setLoading(true);
    try {
      // 存储用户名到本地存储，供布局中的头像使用
      localStorage.setItem('username', values.username);
      
      // 使用 authStore 中的 login 函数
      await login(values);
      message.success('登录成功！');
    } catch (error: any) {
      console.error("Login failed:", error);
      setError(error?.response?.data?.detail || error.message || '登录失败，请检查您的用户名和密码');
      message.error('登录失败，请检查您的用户名和密码');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isAuthenticated) {
      navigate('/home');
    }
  }, [isAuthenticated, navigate]);

  return (
    <div className="login-container">
      <div className="login-wrapper">
        <div className="login-logo">
          <div className="logo-container">
            <div className="logo-icon">FMS</div>
          </div>
          <Title level={2} className="system-title">数据标注系统</Title>
        </div>
        
        <div className="login-content">
          {error && (
            <Alert
              message="登录错误"
              description={error}
              type="error"
              showIcon
              style={{ marginBottom: 16 }}
            />
          )}
          
          <Form
            name="login"
            initialValues={{ remember: true }}
            onFinish={onFinish}
            autoComplete="off"
            className="login-form"
            layout="vertical"
          >
            <Title level={3} style={{ marginBottom: 24, textAlign: 'center' }}>用户登录</Title>
            
            <Form.Item
              name="username"
              rules={[{ required: true, message: '请输入您的用户名！' }]}
            >
              <Input 
                prefix={<UserOutlined className="site-form-item-icon" />} 
                placeholder="用户名" 
                size="large"
              />
            </Form.Item>

            <Form.Item
              name="password"
              rules={[{ required: true, message: '请输入您的密码！' }]}
            >
              <Input.Password 
                prefix={<LockOutlined className="site-form-item-icon" />} 
                placeholder="密码" 
                size="large"
              />
            </Form.Item>

            <Form.Item>
              <Button 
                type="primary" 
                htmlType="submit" 
                className="login-button" 
                size="large"
                loading={loading}
              >
                登录
              </Button>
            </Form.Item>
            
            <Divider />
            
            <div className="login-footer">
              <Text type="secondary">默认管理员账号: <EMAIL></Text>
              <br />
              <Text type="secondary">默认密码: password</Text>
            </div>
          </Form>
        </div>
      </div>
    </div>
  );
};

export default Login;