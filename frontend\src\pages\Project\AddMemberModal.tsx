import React, { useState, useEffect } from 'react';
import { Modal, Form, Select, message, Spin } from 'antd';
import apiClient from '../../api/client';
import { AxiosError } from 'axios';

interface User {
  id: number;
  username: string;
}

interface AddMemberModalProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
  projectId: number;
}

const AddMemberModal: React.FC<AddMemberModalProps> = ({ visible, onClose, onSuccess, projectId }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [users, setUsers] = useState<User[]>([]);
  const [fetchingUsers, setFetchingUsers] = useState(false);

  const fetchUsers = async () => {
    setFetchingUsers(true);
    try {
      const response = await apiClient.get<User[]>('/users/');
      setUsers(response.data);
    } catch (err) {
      message.error('获取用户列表失败');
    } finally {
      setFetchingUsers(false);
    }
  };

  useEffect(() => {
    if (visible) {
      fetchUsers();
    }
  }, [visible]);

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);
      await apiClient.post(`/projects/${projectId}/members`, { user_id: values.userId });
      message.success('成员添加成功！');
      onSuccess();
      form.resetFields();
    } catch (err) {
      const axiosError = err as AxiosError;
      message.error(axiosError.message || '成员添加失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title="添加新成员"
      visible={visible}
      onOk={handleOk}
      onCancel={onClose}
      confirmLoading={loading}
    >
      <Form form={form} layout="vertical" name="add_member_form">
        <Form.Item
          name="userId"
          label="选择用户"
          rules={[{ required: true, message: '请选择一个用户！' }]}
        >
          <Select
            showSearch
            placeholder="搜索并选择用户"
            loading={fetchingUsers}
            filterOption={(input, option) =>
              (option?.label ?? '').toString().toLowerCase().includes(input.toLowerCase())
            }
            options={users.map(user => ({ label: user.username, value: user.id }))}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default AddMemberModal; 