import React from 'react';
import { Modal, Form, Input, message } from 'antd';
import apiClient from '../../api/client';
import { AxiosError } from 'axios';

interface CreateProjectModalProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const CreateProjectModal: React.FC<CreateProjectModalProps> = ({ visible, onClose, onSuccess }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = React.useState(false);

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);
      await apiClient.post('/projects/', values);
      message.success('项目创建成功！');
      onSuccess();
      form.resetFields();
    } catch (err) {
      const axiosError = err as AxiosError;
      message.error(axiosError.message || '项目创建失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title="创建新项目"
      visible={visible}
      onOk={handleOk}
      onCancel={onClose}
      confirmLoading={loading}
    >
      <Form form={form} layout="vertical" name="create_project_form">
        <Form.Item
          name="name"
          label="项目名称"
          rules={[{ required: true, message: '请输入项目名称！' }]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          name="description"
          label="项目描述"
        >
          <Input.TextArea rows={4} />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default CreateProjectModal; 