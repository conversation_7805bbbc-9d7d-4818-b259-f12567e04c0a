import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Spin, Result, Button, Typography, Space, Table, Tabs } from 'antd';
import { ArrowLeftOutlined, PlusOutlined } from '@ant-design/icons';
import apiClient from '../../api/client';
import { AxiosError } from 'axios';
import AddMemberModal from './AddMemberModal';

// We'll need a more detailed project type later
interface Project {
  id: number;
  name: string;
  description: string;
  members: any[]; // Assuming a simple structure for members
}

interface Dataset {
  id: number;
  name: string;
  description: string;
}

const { TabPane } = Tabs;

const ProjectDetail: React.FC = () => {
    const { projectId } = useParams<{ projectId: string }>();
    const navigate = useNavigate();
    const [project, setProject] = useState<Project | null>(null);
    const [datasets, setDatasets] = useState<Dataset[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [isAddMemberModalVisible, setIsAddMemberModalVisible] = useState(false);

    const fetchData = async () => {
        if (!projectId) return;
        setLoading(true);
        try {
            const projectPromise = apiClient.get<Project>(`/projects/${projectId}`);
            const datasetPromise = apiClient.get<Dataset[]>('/datasets/', { params: { project_id: projectId } });
            
            const [projectResponse, datasetResponse] = await Promise.all([projectPromise, datasetPromise]);

            setProject(projectResponse.data);
            setDatasets(datasetResponse.data);
            setError(null);
        } catch (err) {
            const axiosError = err as AxiosError;
            setError(axiosError.message || `获取项目数据失败`);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchData();
    }, [projectId]);

    const handleAddMemberSuccess = () => {
        setIsAddMemberModalVisible(false);
        fetchData(); // Refresh project data, including members
    };

    if (loading) {
        return <Spin size="large" className="global-spin" />;
    }

    if (error || !project) {
        return (
            <Result
                status="error"
                title="加载失败"
                subTitle={error || '找不到该项目。'}
                extra={<Button type="primary" onClick={fetchData}>重试</Button>}
            />
        );
    }

    const datasetColumns = [
        { title: '数据集名称', dataIndex: 'name', key: 'name' },
        { title: '描述', dataIndex: 'description', key: 'description' },
        { title: '操作', key: 'action', render: () => <Button type="link">详情</Button> },
    ];

    const memberColumns = [
        { title: '成员名称', dataIndex: 'name', key: 'name' },
        { title: '操作', key: 'action', render: () => <Button type="link">详情</Button> },
    ];

    return (
        <Space direction="vertical" style={{ width: '100%' }} size="large">
             <Space align="center" style={{ marginBottom: 16 }}>
                <Button icon={<ArrowLeftOutlined />} onClick={() => navigate(-1)} />
                <Typography.Title level={4} style={{ margin: 0 }}>
                    {project.name}
                </Typography.Title>
            </Space>

            <Tabs defaultActiveKey="1">
                <TabPane tab="数据集" key="1">
                    <div style={{ marginBottom: 16, textAlign: 'right' }}>
                        <Button type="primary" icon={<PlusOutlined />}>添加数据集</Button>
                    </div>
                    <Table columns={datasetColumns} dataSource={datasets} rowKey="id" />
                </TabPane>
                <TabPane tab="标注团队" key="2">
                    <div style={{ marginBottom: 16, textAlign: 'right' }}>
                        <Button type="primary" icon={<PlusOutlined />} onClick={() => setIsAddMemberModalVisible(true)}>添加成员</Button>
                    </div>
                    <Table columns={memberColumns} dataSource={project?.members} rowKey="id" />
                </TabPane>
                <TabPane tab="项目设置" key="3">
                    <p>项目设置功能待开发。</p>
                </TabPane>
            </Tabs>
            
            {project && (
                <AddMemberModal
                    visible={isAddMemberModalVisible}
                    onClose={() => setIsAddMemberModalVisible(false)}
                    onSuccess={handleAddMemberSuccess}
                    projectId={project.id}
                />
            )}
        </Space>
    );
};

export default ProjectDetail; 