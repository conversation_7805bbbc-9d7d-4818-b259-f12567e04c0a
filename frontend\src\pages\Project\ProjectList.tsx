import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Table, Button, Spin, Result, message, Typography, Space } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import apiClient from '../../api/client';
import { AxiosError } from 'axios';
import CreateProjectModal from './CreateProjectModal';

interface Project {
  id: number;
  name: string;
  description: string;
  owner_id: number;
}

const ProjectList: React.FC = () => {
    const [projects, setProjects] = useState<Project[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [isModalVisible, setIsModalVisible] = useState(false);
    const navigate = useNavigate();

    const fetchProjects = async () => {
        try {
            setLoading(true);
            const response = await apiClient.get<Project[]>('/projects/');
            setProjects(response.data);
            setError(null);
        } catch (err) {
            const axiosError = err as AxiosError;
            setError(axiosError.message || '获取项目列表失败');
            message.error(axiosError.message || '获取项目列表失败');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchProjects();
    }, []);

    const handleCreateSuccess = () => {
        setIsModalVisible(false);
        fetchProjects(); // Refresh list after creation
    };

    const columns = [
        {
            title: '项目名称',
            dataIndex: 'name',
            key: 'name',
        },
        {
            title: '描述',
            dataIndex: 'description',
            key: 'description',
        },
        {
            title: '操作',
            key: 'action',
            render: (_: any, record: Project) => (
                <Button type="link" onClick={() => navigate(`/projects/${record.id}`)}>查看</Button>
            ),
        },
    ];

    if (loading) {
        return <Spin size="large" className="global-spin" />;
    }

    if (error) {
        return (
            <Result
                status="error"
                title="加载失败"
                subTitle={error}
                extra={<Button type="primary" onClick={fetchProjects}>重试</Button>}
            />
        );
    }

    return (
        <Space direction="vertical" style={{ width: '100%' }} size="large">
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography.Title level={4}>全部项目</Typography.Title>
                <Button type="primary" icon={<PlusOutlined />} onClick={() => setIsModalVisible(true)}>
                    创建项目
                </Button>
            </div>
            <Table columns={columns} dataSource={projects} rowKey="id" />
            <CreateProjectModal
                visible={isModalVisible}
                onClose={() => setIsModalVisible(false)}
                onSuccess={handleCreateSuccess}
            />
        </Space>
    );
};

export default ProjectList; 