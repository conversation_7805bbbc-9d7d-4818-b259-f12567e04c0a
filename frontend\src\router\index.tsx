import { createBrowserRouter, RouterProvider, Navigate } from 'react-router-dom';
import Login from '../pages/Login';
import AppLayout from '../layouts/AppLayout';
import EmptyPage from '../pages/EmptyPage';
import ProtectedRoute from './ProtectedRoute';
import ProjectList from '../pages/Project/ProjectList';
import ProjectDetail from '../pages/Project/ProjectDetail';
import React from 'react';

// 创建空页面组件的函数，带有不同标题
const createEmptyPage = (title: string) => {
  return () => <EmptyPage title={title} />;
};

const router = createBrowserRouter([
  {
    path: '/login',
    element: <Login />,
  },
  {
    path: '/',
    element: (
      <ProtectedRoute>
        <AppLayout />
      </ProtectedRoute>
    ),
    children: [
      {
        index: true,
        element: <Navigate to="/home/<USER>" replace />,
      },
      // 首页模块
      {
        path: 'home',
        children: [
            { index: true, element: <Navigate to="progress-stats" replace /> },
            { path: 'progress-stats', element: createEmptyPage("标注进度")() },
            { path: 'quality-metrics', element: createEmptyPage("质量指标")() },
            { path: 'personal-stats', element: createEmptyPage("个人统计")() },
            { path: 'pending-tasks', element: createEmptyPage("待处理任务")() },
            { path: 'completed-tasks', element: createEmptyPage("已完成任务")() },
            { path: 'qa-tasks', element: createEmptyPage("质检任务")() },
            { path: 'system-notifications', element: createEmptyPage("系统通知")() },
            { path: 'task-notifications', element: createEmptyPage("任务通知")() },
            { path: 'qa-feedback', element: createEmptyPage("质检反馈")() },
        ],
      },
      
      // 项目管理模块
      {
        path: 'projects',
        children: [
            { index: true, element: <Navigate to="all-projects" replace /> },
            { path: 'all-projects', element: <ProjectList /> },
            { path: ':projectId', element: <ProjectDetail /> },
            { path: 'my-projects', element: createEmptyPage("我的项目")() },
            { path: 'archived-projects', element: createEmptyPage("已归档项目")() },
            { path: 'project-create', element: createEmptyPage("项目创建")() },
            { path: 'progress-monitoring', element: createEmptyPage("进度监控")() },
            { path: 'quality-monitoring', element: createEmptyPage("质量监控")() },
            { path: 'member-performance', element: createEmptyPage("成员表现")() },
            { path: 'annotation-guides', element: createEmptyPage("标注指南")() },
            { path: 'faqs', element: createEmptyPage("常见问题")() },
            { path: 'standard-examples', element: createEmptyPage("标准示例")() },
        ],
      },

      // 数据集模块
      {
        path: 'datasets',
        children: [
            { index: true, element: <Navigate to="dataset-list" replace /> },
            { path: 'dataset-list', element: createEmptyPage("数据集列表")() },
            { path: 'dataset-versions', element: createEmptyPage("数据集版本")() },
            { path: 'import-data', element: createEmptyPage("数据导入")() },
            { path: 'preprocess-data', element: createEmptyPage("数据预处理")() },
            { path: 'export-data', element: createEmptyPage("数据导出")() },
        ]
      },

      // 标注工作台模块
      {
        path: 'workspace',
        children: [
            { index: true, element: <Navigate to="pending-annotation" replace /> },
            { path: 'pending-annotation', element: createEmptyPage("待标注任务")() },
            { path: 'annotation-history', element: createEmptyPage("标注历史")() },
            { path: 'image-annotation', element: createEmptyPage("图像标注")() },
            { path: 'text-annotation', element: createEmptyPage("文本标注")() },
            { path: 'audio-annotation', element: createEmptyPage("音频标注")() },
            { path: 'video-annotation', element: createEmptyPage("视频标注")() },
            { path: 'pending-qa', element: createEmptyPage("待质检任务")() },
            { path: 'qa-history', element: createEmptyPage("质检历史")() },
            { path: 'difficult-samples', element: createEmptyPage("疑难样本")() },
            { path: 'rule-discussions', element: createEmptyPage("规则讨论")() },
        ]
      },

      // 测试模块
      {
        path: 'testing',
        children: [
            { index: true, element: <Navigate to="test-overview" replace /> },
            { path: 'test-overview', element: createEmptyPage("测试概览")() },
            { path: 'test-progress', element: createEmptyPage("测试进度")() },
            { path: 'test-quality', element: createEmptyPage("质量报告")() },
            { path: 'test-case-library', element: createEmptyPage("用例库")() },
            { path: 'create-test-case', element: createEmptyPage("创建用例")() },
            { path: 'execute-test-case', element: createEmptyPage("用例执行")() },
            { path: 'defect-list', element: createEmptyPage("缺陷列表")() },
            { path: 'defect-statistics', element: createEmptyPage("缺陷统计")() },
            { path: 'defect-analysis', element: createEmptyPage("缺陷分析")() },
            { path: 'environment-settings', element: createEmptyPage("环境配置")() },
            { path: 'test-plans', element: createEmptyPage("测试计划")() },
        ]
      },

      // 系统管理模块
      {
        path: 'system',
        children: [
            { index: true, element: <Navigate to="user-list" replace /> },
            { path: 'user-list', element: createEmptyPage("用户列表")() },
            { path: 'roles-permissions', element: createEmptyPage("角色权限")() },
            { path: 'team-management', element: createEmptyPage("团队管理")() },
            { path: 'label-management', element: createEmptyPage("标签体系")() },
            { path: 'template-management', element: createEmptyPage("标注模板")() },
            { path: 'shortcut-settings', element: createEmptyPage("快捷键设置")() },
            { path: 'qa-rules', element: createEmptyPage("质检规则")() },
            { path: 'consistency-check', element: createEmptyPage("一致性检查")() },
            { path: 'auto-qa-config', element: createEmptyPage("自动质检")() },
            { path: 'system-logs', element: createEmptyPage("系统日志")() },
        ]
      },
      
      // 通配符路由，处理未匹配的路径
      {
        path: '*',
        element: <Navigate to="/home/<USER>" replace />,
      },
    ],
  },
]);

export const AppRouter = () => <RouterProvider router={router} />;