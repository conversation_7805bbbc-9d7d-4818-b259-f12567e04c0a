import client from '../api/client';
import { message } from 'antd';
import type { PaginatedResponse, ApiError } from '../types';
import config from '../config';

// 通用错误处理
const handleError = (error: any) => {
  const errorMessage = error.response?.data?.detail || error.message || '请求失败';
  message.error(errorMessage);
  console.error('API 请求错误:', error);
  return Promise.reject(error);
};

// 通用 API 服务
export const apiService = {
  // 通用 GET 请求
  async get<T>(url: string, params?: Record<string, any>): Promise<T> {
    try {
      const response = await client.get<T>(url, { params });
      return response.data;
    } catch (error) {
      return handleError(error);
    }
  },
  
  // 通用 POST 请求
  async post<T>(url: string, data?: any): Promise<T> {
    try {
      const response = await client.post<T>(url, data);
      return response.data;
    } catch (error) {
      return handleError(error);
    }
  },
  
  // 通用 PUT 请求
  async put<T>(url: string, data?: any): Promise<T> {
    try {
      const response = await client.put<T>(url, data);
      return response.data;
    } catch (error) {
      return handleError(error);
    }
  },
  
  // 通用 DELETE 请求
  async delete<T>(url: string): Promise<T> {
    try {
      const response = await client.delete<T>(url);
      return response.data;
    } catch (error) {
      return handleError(error);
    }
  },
  
  // 分页 GET 请求
  async getPaginated<T>(url: string, page = 1, pageSize = config.pagination.defaultPageSize, params?: Record<string, any>): Promise<PaginatedResponse<T>> {
    try {
      const response = await client.get<PaginatedResponse<T>>(url, {
        params: {
          ...params,
          page,
          size: pageSize
        }
      });
      return response.data;
    } catch (error) {
      return handleError(error);
    }
  }
}; 