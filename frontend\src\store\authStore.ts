import { create } from 'zustand';
import apiClient from '../api/client';

interface AuthState {
  token: string | null;
  user: any | null;
  isAuthenticated: boolean;
  login: (values: any) => Promise<void>;
  logout: () => void;
  checkAuth: () => void;
}

const useAuthStore = create<AuthState>((set, get) => ({
  token: localStorage.getItem('access_token'),
  user: JSON.parse(localStorage.getItem('user') || 'null'),
  isAuthenticated: !!localStorage.getItem('access_token'),
  
  login: async (values) => {
    try {
      console.log('Login attempt with:', values.username);
      
      // Convert JSON object to URL-encoded form data
      const params = new URLSearchParams();
      params.append('username', values.username);
      params.append('password', values.password);

      console.log('Sending login request with params:', params.toString());
      
      const response = await apiClient.post('/login', params, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });

      console.log('Login response:', response.data);

      if (response.data && response.data.access_token) {
        const token = response.data.access_token;
        const user = response.data.user || {
          username: values.username
        };
        
        // 存储 token 和用户信息
        localStorage.setItem('access_token', token);
        localStorage.setItem('user', JSON.stringify(user));
        localStorage.setItem('username', user.username);

        console.log('Login successful, user:', user);
        
        set({
          isAuthenticated: true,
          user: user,
          token: token,
        });
      } else {
        console.error('Login response missing access token:', response.data);
        throw new Error('Login response missing access token');
      }
    } catch (error) {
      console.error('Login error:', error);
      // Clear any partial login artifacts
      get().logout();
      throw error;
    }
  },
  
  logout: () => {
    localStorage.removeItem('access_token');
    localStorage.removeItem('user');
    localStorage.removeItem('username');
    set({ token: null, user: null, isAuthenticated: false });
  },
  
  checkAuth: () => {
    const token = localStorage.getItem('access_token');
    const user = localStorage.getItem('user');
    if (token && user) {
      set({ isAuthenticated: true, user: JSON.parse(user), token });
    } else {
      get().logout();
    }
  },
}));

export default useAuthStore;
