// 用户相关类型
export interface User {
  id: number;
  username: string;
  email: string;
  role: 'admin' | 'user';
}

// 认证相关类型
export interface AuthResponse {
  access_token: string;
  token_type: string;
  user: User;
  msg: string;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

// 项目相关类型
export interface Project {
  id: number;
  name: string;
  description: string;
  owner_id: number;
}

export interface ProjectCreate {
  title: string;
  description: string;
}

export interface ProjectUpdate {
  title?: string;
  description?: string;
}

// 数据集相关类型
export interface Dataset {
  id: number;
  name: string;
  description: string;
  file_path: string;
  created_at: string;
  owner_id: number;
  owner?: User;
}

export interface DatasetCreate {
  name: string;
  description: string;
  file_path: string;
}

// 分页响应
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

// API 错误响应
export interface ApiError {
  detail: string;
  status_code?: number;
} 